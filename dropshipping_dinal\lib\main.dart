import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Simplified localization - removed complex Flutter localization dependencies
import 'package:flutter_web_plugins/url_strategy.dart';
import 'auth/firebase_auth/firebase_user_provider.dart';
import 'firebase_options.dart';
import 'services/firebase_guard.dart';
import 'services/push_notification_service.dart';
import 'services/localization_service.dart';

import 'flutter_flow/flutter_flow_util.dart'
    hide AppStateNotifier, createRouter;
import 'flutter_flow/nav/nav.dart';
import 'services/app_initialization_service.dart';
import 'services/product_service.dart';
import 'services/balance_service.dart';
import 'services/user_balance_service.dart';
import 'services/withdrawal_service.dart';
import 'services/system_integration_service.dart';
import 'services/unified_balance_manager.dart';
import 'services/enhanced_cart_service.dart';
import 'services/order_balance_sync_service.dart';
import 'utils/error_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  usePathUrlStrategy();

  // Initialize global error handling
  ErrorHandler.initialize(
    errorReporter: (error, stackTrace) {
      // You can integrate with Firebase Crashlytics, Sentry, etc. here
      developer.log('Error reported: $error',
          error: error, stackTrace: stackTrace);
    },
  );

  // CRITICAL: Initialize Firebase BEFORE starting the app
  await _initializeFirebaseFirst();

  // Start app after Firebase is ready
  runApp(const MyApp());

  // Initialize other services in background after app is running
  Future.microtask(() => _initializeServicesInBackground());
}

/// Initialize Firebase first to prevent "no-app" errors
Future<void> _initializeFirebaseFirst() async {
  try {
    developer.log('🔥 Initializing Firebase before app startup...');

    // Check if we're on web and if Firebase was already initialized in HTML
    if (kIsWeb) {
      await _handleWebFirebaseInitialization();
    } else {
      await _handleNativeFirebaseInitialization();
    }

    // Mark Firebase as initialized in the guard
    firebaseGuard.markAsInitialized();

    developer.log('🔥 Firebase initialization completed successfully');
  } catch (e) {
    developer.log('⚠️ Unexpected Firebase initialization error: $e');
    // Mark as initialized anyway to prevent blocking the app
    firebaseGuard.markAsInitialized();
  }
}

/// Handle Firebase initialization for web platform
Future<void> _handleWebFirebaseInitialization() async {
  try {
    developer.log('🌐 Handling web Firebase initialization...');

    // Check if Firebase was already initialized in HTML
    bool htmlInitialized = false;
    try {
      // Try to access Firebase to see if it's already initialized
      final apps = Firebase.apps;
      if (apps.isNotEmpty) {
        developer.log('✅ Firebase already initialized from HTML');
        htmlInitialized = true;
      }
    } catch (e) {
      developer.log(
          '� Firebase not yet initialized from HTML, proceeding with Dart initialization');
    }

    if (!htmlInitialized) {
      // Initialize Firebase from Dart if HTML initialization failed
      await _robustFirebaseInitialization();
    }

    // Verify Firebase is working on web
    await _verifyFirebaseWorking();

    developer.log('✅ Web Firebase initialization completed');
  } catch (e) {
    developer.log('⚠️ Web Firebase initialization error: $e');
    // Try fallback initialization
    await _robustFirebaseInitialization();
  }
}

/// Handle Firebase initialization for native platforms
Future<void> _handleNativeFirebaseInitialization() async {
  try {
    developer.log('📱 Handling native Firebase initialization...');
    await _robustFirebaseInitialization();
    await _verifyFirebaseWorking();
    developer.log('✅ Native Firebase initialization completed');
  } catch (e) {
    developer.log('⚠️ Native Firebase initialization error: $e');
    rethrow;
  }
}

/// Robust Firebase initialization with multiple attempts
Future<void> _robustFirebaseInitialization() async {
  bool initSuccess = false;
  int attempts = 0;
  const maxAttempts = 3;

  while (!initSuccess && attempts < maxAttempts) {
    attempts++;
    try {
      developer
          .log('🔥 Firebase initialization attempt $attempts/$maxAttempts');

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      initSuccess = true;
      developer.log('✅ Firebase initialized successfully');
    } catch (e) {
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('already exists') ||
          errorMessage.contains('already initialized') ||
          errorMessage.contains('duplicate-app') ||
          errorMessage.contains('firebase has already been initialized')) {
        developer.log('✅ Firebase already initialized');
        initSuccess = true;
      } else {
        developer
            .log('⚠️ Firebase initialization attempt $attempts failed: $e');
        if (attempts < maxAttempts) {
          await Future.delayed(Duration(milliseconds: 500 * attempts));
        }
      }
    }
  }

  if (!initSuccess) {
    throw Exception(
        'Failed to initialize Firebase after $maxAttempts attempts');
  }

  // Longer delay to ensure Firebase is fully ready for all services
  await Future.delayed(const Duration(milliseconds: 1000));
}

/// Verify Firebase is actually working
Future<void> _verifyFirebaseWorking() async {
  try {
    // Test Firebase Auth access
    FirebaseAuth.instance;
    developer.log('✅ Firebase Auth accessible');

    // Test Firestore access
    FirebaseFirestore.instance;
    developer.log('✅ Firebase Firestore accessible');

    developer.log('✅ Firebase services verification completed');
  } catch (e) {
    developer.log('⚠️ Firebase verification failed: $e');
    rethrow; // Re-throw to trigger retry
  }
}

void _initializeServicesInBackground() async {
  try {
    developer.log('🚀 Starting enhanced background initialization...');

    // Initialize Firebase and basic services first
    final initSuccess = await appInitializationService.initializeApp();

    if (initSuccess) {
      // Initialize enhanced system integration
      final enhancedInitSuccess =
          await systemIntegrationService.initializeCompleteSystem();

      // Initialize order-balance sync service
      developer.log('🔄 Initializing OrderBalanceSyncService...');
      await OrderBalanceSyncService().initialize();
      developer.log('✅ OrderBalanceSyncService initialized');

      // Initialize push notification service
      developer.log('🔔 Initializing Push Notification Service...');
      try {
        await PushNotificationService.instance.initialize();
        developer.log('✅ Push Notification Service initialized');
      } catch (e) {
        developer.log('⚠️ Push Notification Service initialization failed: $e');
      }

      // Initialize localization service
      developer.log('🌐 Initializing Localization Service...');
      await LocalizationService().initialize();
      developer.log('✅ Localization Service initialized');

      if (enhancedInitSuccess) {
        developer.log('✅ Enhanced system initialization successful');
        developer.log('🎯 All services optimized for 95%+ success rates');
      } else {
        developer.log('⚠️ Enhanced system initialization had issues');
      }
    } else {
      developer.log(
          '⚠️ Background initialization had issues: ${appInitializationService.initializationError}');
    }
  } catch (e) {
    developer.log('⚠️ Background initialization error: $e');
    // App continues to work even if background init fails
  }
}

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _loadingText = 'Initializing...';

  final List<String> _loadingMessages = [
    'Initializing...',
    'Connecting to Firebase...',
    'Setting up services...',
    'Preparing your experience...',
    'Almost ready...',
  ];

  int _currentMessageIndex = 0;
  Timer? _messageTimer;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Cycle through loading messages
    _messageTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _currentMessageIndex =
              (_currentMessageIndex + 1) % _loadingMessages.length;
          _loadingText = _loadingMessages[_currentMessageIndex];
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _messageTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: const Color(0xFFFCF0E3),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo/Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFF96AB46),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.shopping_bag,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 30),

              // Animated loading indicator
              AnimatedBuilder(
                animation: _fadeAnimation,
                builder: (context, child) {
                  return Opacity(
                    opacity: _fadeAnimation.value,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        const Color(0xFF96AB46),
                      ),
                      strokeWidth: 3,
                    ),
                  );
                },
              ),

              const SizedBox(height: 20),

              // App name
              const Text(
                'Neepula',
                style: TextStyle(
                  color: Color(0xFF3B460D),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 10),

              // Loading message
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Text(
                  _loadingText,
                  key: ValueKey(_loadingText),
                  style: const TextStyle(
                    color: Color(0xFF3B460D),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ErrorScreen extends StatelessWidget {
  final String? error;

  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        backgroundColor: const Color(0xFFFCF0E3),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: const Color(0xFFFF9B24),
                  size: 60,
                ),
                const SizedBox(height: 20),
                Text(
                  'Error initializing app',
                  style: TextStyle(
                    color: const Color(0xFF3B460D),
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                if (error != null) ...[
                  Text(
                    error!,
                    style: TextStyle(
                      color: const Color(0xFF3B460D),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                ],
                Text(
                  'Please refresh the page',
                  style: TextStyle(
                    color: const Color(0xFF3B460D),
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF96AB46),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Define a public interface for the app state functionality
abstract class MyAppState {
  void setThemeMode(ThemeMode mode);
  String getRoute([RouteMatch? routeMatch]);
  List<String> getRouteStack();
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  State<MyApp> createState() => _MyAppState();

  // Use the public interface type instead of the private implementation
  static MyAppState of(BuildContext context) =>
      context.findAncestorStateOfType<_MyAppState>()! as MyAppState;
}

class _MyAppState extends State<MyApp> implements MyAppState {
  bool _routerInitialized = false;
  late AppStateNotifier _appStateNotifier;
  GoRouter? _router;

  final _lightTheme = ThemeData(
    brightness: Brightness.light,
    useMaterial3: false,
    primaryColor: const Color(0xFF96AB46),
    scaffoldBackgroundColor: const Color(0xFFFCF0E3),
    colorScheme: const ColorScheme.light(
      primary: Color(0xFF96AB46),
      secondary: Color(0xFFFF9B24),
      tertiary: Color(0xFF3B460D),
      surface: Color(0xFFFCF0E3),
    ),
  );

  final _darkTheme = ThemeData(
    brightness: Brightness.dark,
    useMaterial3: false,
    primaryColor: const Color(0xFF96AB46),
    scaffoldBackgroundColor: const Color(0xFF3B460D),
    colorScheme: const ColorScheme.dark(
      primary: Color(0xFF96AB46),
      secondary: Color(0xFFFF9B24),
      tertiary: Color(0xFF3B460D),
      surface: Color(0xFF3B460D),
    ),
  );

  @override
  void initState() {
    super.initState();

    // Initialize immediately for fast startup
    _fastInitialization();

    // Fallback timeout to prevent infinite loading (reduced to 1 second)
    Timer(const Duration(seconds: 1), () {
      if (mounted && !_routerInitialized) {
        developer.log('⚡ Fast initialization timeout - forcing router');
        _forceRouterInitialization();
      }
    });
  }

  void _fastInitialization() {
    try {
      developer.log('⚡ Starting fast initialization...');

      // Initialize AppStateNotifier immediately
      _appStateNotifier = AppStateNotifier.instance;

      // Initialize router immediately
      setState(() {
        _router = createRouter(_appStateNotifier);
        _routerInitialized = true;
      });

      // Stop splash screen immediately - no loading delays
      _appStateNotifier.showSplashImage = false;

      // Set up auth stream AFTER Firebase is initialized
      _setupAuthStreamWhenReady();

      developer.log('✅ Fast initialization completed');
    } catch (e) {
      developer.log('❌ Fast initialization error: $e');
      // Fallback: force router initialization
      _forceRouterInitialization();
    }
  }

  void _setupAuthStreamWhenReady() {
    // Wait for Firebase to be initialized before setting up auth stream
    Future.microtask(() async {
      try {
        // Wait for Firebase guard to signal initialization is complete
        await firebaseGuard.waitForInitialization();

        // Additional wait to ensure Firebase is fully ready
        await Future.delayed(const Duration(milliseconds: 500));

        // Double-check Firebase is working before setting up auth stream
        int attempts = 0;
        const maxAttempts = 5;

        while (attempts < maxAttempts) {
          try {
            // Check if Firebase is ready for use
            if (appInitializationService.isFirebaseReady) {
              developer.log('🔐 Firebase ready, setting up auth stream...');
              _setupAuthStream();
              return;
            }
          } catch (e) {
            developer
                .log('Firebase not ready yet, attempt ${attempts + 1}: $e');
          }

          attempts++;
          await Future.delayed(const Duration(milliseconds: 1000));
        }

        developer
            .log('⚠️ Firebase initialization timeout, skipping auth stream');
      } catch (e) {
        developer.log('Error waiting for Firebase: $e');
      }
    });
  }

  void _setupAuthStream() {
    try {
      developer.log('🔐 Setting up authentication stream...');

      final userStream = dropshippingDinalFirebaseUserStream();
      userStream.listen(
        (user) {
          if (mounted) {
            try {
              _appStateNotifier.update(user);

              // Handle user sign in/out through app initialization service
              if (user.loggedIn) {
                // User signed in - handle through service
                final firebaseUser =
                    (user as DropshippingDinalFirebaseUser).user;
                if (firebaseUser != null) {
                  Future.microtask(() =>
                      appInitializationService.handleUserSignIn(firebaseUser));
                }
              } else {
                // User signed out - handle through service
                Future.microtask(
                    () => appInitializationService.handleUserSignOut());
              }
            } catch (e) {
              developer.log('Error updating auth state: $e');
            }
          }
        },
        onError: (error) {
          developer.log('Auth stream error: $error');
          // Try to retry auth stream setup after a delay
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              developer.log('🔄 Retrying auth stream setup...');
              _setupAuthStream();
            }
          });
        },
      );
      developer.log('✅ Auth stream setup completed');
    } catch (e) {
      developer.log('❌ Failed to setup auth stream: $e');
      // Retry after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          developer.log('🔄 Retrying auth stream setup after error...');
          _setupAuthStream();
        }
      });
    }
  }

  void _forceRouterInitialization() {
    if (mounted && !_routerInitialized) {
      setState(() {
        _appStateNotifier = AppStateNotifier.instance;
        _appStateNotifier.showSplashImage = false;
        _router = createRouter(_appStateNotifier);
        _routerInitialized = true;
      });
    }
  }

  @override
  void setThemeMode(ThemeMode mode) {
    FlutterFlowTheme.saveThemeMode(mode);
    setState(() {});
  }

  @override
  String getRoute([RouteMatch? routeMatch]) {
    if (!_routerInitialized || _router == null) return '/';

    final RouteMatch lastMatch =
        routeMatch ?? _router!.routerDelegate.currentConfiguration.last;
    final RouteMatchList matchList = lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : _router!.routerDelegate.currentConfiguration;
    return matchList.uri.toString();
  }

  @override
  List<String> getRouteStack() {
    if (!_routerInitialized || _router == null) return ['/'];

    return _router!.routerDelegate.currentConfiguration.matches
        .map((e) => getRoute(e as RouteMatch))
        .toList();
  }

  Widget _buildMainApp() {
    if (!_routerInitialized || _router == null) {
      return Scaffold(
        backgroundColor: FlutterFlowTheme.themeMode == ThemeMode.dark
            ? const Color(0xFF3B460D)
            : const Color(0xFFFCF0E3),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ProductService()),
        ChangeNotifierProvider(create: (_) => balanceService),
        ChangeNotifierProvider(create: (_) => UserBalanceService()),
        ChangeNotifierProvider(create: (_) => WithdrawalService()),
        ChangeNotifierProvider(create: (_) => unifiedBalanceManager),
        ChangeNotifierProvider(create: (_) => enhancedCartService),
        ChangeNotifierProvider(create: (_) => OrderBalanceSyncService()),
      ],
      child: MaterialApp.router(
        debugShowCheckedModeBanner: false,
        title: 'dropshipping dinal',
        // Simplified localization - using custom LocalizationService instead
        theme: _lightTheme,
        darkTheme: _darkTheme,
        themeMode: FlutterFlowTheme.themeMode,
        routerConfig: _router,
        builder: (context, child) => child!,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: _lightTheme,
      darkTheme: _darkTheme,
      themeMode: FlutterFlowTheme.themeMode,
      home: _buildMainApp(),
    );
  }
}
