const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

/**
 * Send push notification to a specific user
 */
exports.sendNotificationToUser = functions.https.onCall(async (data, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { userId, title, body, notificationData, type } = data;

    if (!userId || !title || !body) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Get user's FCM token
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'User not found');
    }

    const userData = userDoc.data();
    const fcmToken = userData.fcmToken;

    if (!fcmToken) {
      console.log(`No FCM token found for user: ${userId}`);
      return { success: false, message: 'No FCM token found' };
    }

    // Prepare notification payload
    const message = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      data: {
        type: type || 'general',
        ...notificationData,
      },
      android: {
        notification: {
          channelId: 'dropshipping_channel',
          priority: 'high',
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    // Send notification
    const response = await admin.messaging().send(message);
    console.log('Successfully sent message:', response);

    return { success: true, messageId: response };
  } catch (error) {
    console.error('Error sending notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

/**
 * Send push notification to multiple users
 */
exports.sendNotificationToUsers = functions.https.onCall(async (data, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { userIds, title, body, notificationData, type } = data;

    if (!userIds || !Array.isArray(userIds) || !title || !body) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    const results = [];

    // Get FCM tokens for all users
    const userDocs = await admin.firestore().collection('users').where(admin.firestore.FieldPath.documentId(), 'in', userIds).get();
    
    const tokens = [];
    userDocs.forEach(doc => {
      const userData = doc.data();
      if (userData.fcmToken) {
        tokens.push(userData.fcmToken);
      }
    });

    if (tokens.length === 0) {
      return { success: false, message: 'No valid FCM tokens found' };
    }

    // Prepare multicast message
    const message = {
      tokens: tokens,
      notification: {
        title: title,
        body: body,
      },
      data: {
        type: type || 'general',
        ...notificationData,
      },
      android: {
        notification: {
          channelId: 'dropshipping_channel',
          priority: 'high',
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    // Send multicast notification
    const response = await admin.messaging().sendMulticast(message);
    console.log('Successfully sent multicast message:', response);

    return { 
      success: true, 
      successCount: response.successCount,
      failureCount: response.failureCount,
      responses: response.responses 
    };
  } catch (error) {
    console.error('Error sending multicast notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notifications');
  }
});

/**
 * Trigger when a new order is created
 */
exports.onOrderCreated = functions.firestore
  .document('orders/{orderId}')
  .onCreate(async (snap, context) => {
    try {
      const orderData = snap.data();
      const orderId = context.params.orderId;
      const userId = orderData.userId;

      if (!userId) {
        console.log('No userId found in order data');
        return;
      }

      // Send notification to user
      await sendNotificationToUser(userId, {
        title: 'Order Placed Successfully',
        body: `Your order #${orderId} has been placed and is being processed.`,
        type: 'order',
        orderId: orderId,
        status: 'placed',
        route: '/orders',
      });

      // Send notification to admins
      await sendNotificationToAdmins({
        title: 'New Order Received',
        body: `New order #${orderId} from ${orderData.userName || 'Customer'}`,
        type: 'admin',
        alertType: 'new_order',
        orderId: orderId,
        route: '/adminpanel',
      });

      console.log(`Order creation notifications sent for order: ${orderId}`);
    } catch (error) {
      console.error('Error in onOrderCreated:', error);
    }
  });

/**
 * Trigger when order status is updated
 */
exports.onOrderStatusUpdated = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change, context) => {
    try {
      const beforeData = change.before.data();
      const afterData = change.after.data();
      const orderId = context.params.orderId;

      // Check if status changed
      if (beforeData.status !== afterData.status) {
        const userId = afterData.userId;
        const newStatus = afterData.status;

        if (!userId) {
          console.log('No userId found in order data');
          return;
        }

        let statusMessage = '';
        switch (newStatus) {
          case 'processing':
            statusMessage = 'Your order is being processed';
            break;
          case 'shipped':
            statusMessage = 'Your order has been shipped';
            break;
          case 'delivered':
            statusMessage = 'Your order has been delivered';
            break;
          case 'cancelled':
            statusMessage = 'Your order has been cancelled';
            break;
          default:
            statusMessage = `Your order status has been updated to ${newStatus}`;
        }

        // Send notification to user
        await sendNotificationToUser(userId, {
          title: 'Order Status Update',
          body: statusMessage,
          type: 'order',
          orderId: orderId,
          status: newStatus,
          route: '/orders',
        });

        console.log(`Order status notification sent for order: ${orderId}, status: ${newStatus}`);
      }
    } catch (error) {
      console.error('Error in onOrderStatusUpdated:', error);
    }
  });

/**
 * Trigger when earnings are confirmed
 */
exports.onEarningsConfirmed = functions.firestore
  .document('orders/{orderId}')
  .onUpdate(async (change, context) => {
    try {
      const beforeData = change.before.data();
      const afterData = change.after.data();
      const orderId = context.params.orderId;

      // Check if earnings were confirmed
      if (!beforeData.earningsConfirmed && afterData.earningsConfirmed) {
        const userId = afterData.userId;
        const totalEarnings = afterData.totalEarnings || 0;

        if (!userId) {
          console.log('No userId found in order data');
          return;
        }

        // Send notification to user
        await sendNotificationToUser(userId, {
          title: 'Earnings Confirmed',
          body: `Your earnings of ${totalEarnings} IQD from order #${orderId} have been confirmed and added to your available balance.`,
          type: 'earnings',
          amount: totalEarnings.toString(),
          earningsType: 'available',
          orderId: orderId,
          route: '/account',
        });

        console.log(`Earnings confirmation notification sent for order: ${orderId}, amount: ${totalEarnings}`);
      }
    } catch (error) {
      console.error('Error in onEarningsConfirmed:', error);
    }
  });

/**
 * Helper function to send notification to a specific user
 */
async function sendNotificationToUser(userId, notificationData) {
  try {
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    
    if (!userDoc.exists) {
      console.log(`User not found: ${userId}`);
      return;
    }

    const userData = userDoc.data();
    const fcmToken = userData.fcmToken;

    if (!fcmToken) {
      console.log(`No FCM token found for user: ${userId}`);
      return;
    }

    const message = {
      token: fcmToken,
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      data: {
        type: notificationData.type || 'general',
        ...notificationData,
      },
      android: {
        notification: {
          channelId: 'dropshipping_channel',
          priority: 'high',
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log('Successfully sent message:', response);
    return response;
  } catch (error) {
    console.error('Error sending notification to user:', error);
    throw error;
  }
}

/**
 * Helper function to send notification to all admin users
 */
async function sendNotificationToAdmins(notificationData) {
  try {
    // Get all admin users
    const adminQuery = await admin.firestore()
      .collection('users')
      .where('isAdmin', '==', true)
      .get();

    if (adminQuery.empty) {
      console.log('No admin users found');
      return;
    }

    const tokens = [];
    adminQuery.forEach(doc => {
      const userData = doc.data();
      if (userData.fcmToken) {
        tokens.push(userData.fcmToken);
      }
    });

    if (tokens.length === 0) {
      console.log('No FCM tokens found for admin users');
      return;
    }

    const message = {
      tokens: tokens,
      notification: {
        title: notificationData.title,
        body: notificationData.body,
      },
      data: {
        type: notificationData.type || 'admin',
        ...notificationData,
      },
      android: {
        notification: {
          channelId: 'dropshipping_channel',
          priority: 'high',
          defaultSound: true,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };

    const response = await admin.messaging().sendMulticast(message);
    console.log('Successfully sent admin notifications:', response);
    return response;
  } catch (error) {
    console.error('Error sending notifications to admins:', error);
    throw error;
  }
}
