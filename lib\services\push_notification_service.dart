import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Background message handler - must be top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  developer.log('Handling background message: ${message.messageId}');

  // Handle background notification
  await PushNotificationService.instance._handleBackgroundMessage(message);
}

/// Comprehensive push notification service for the dropshipping app
class PushNotificationService {
  static final PushNotificationService _instance =
      PushNotificationService._internal();
  static PushNotificationService get instance => _instance;
  PushNotificationService._internal();

  // Firebase Messaging instance
  FirebaseMessaging? _messaging;

  // Local notifications plugin
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Notification streams
  final StreamController<RemoteMessage> _messageStreamController =
      StreamController<RemoteMessage>.broadcast();
  final StreamController<String> _tokenStreamController =
      StreamController<String>.broadcast();

  // Public streams
  Stream<RemoteMessage> get messageStream => _messageStreamController.stream;
  Stream<String> get tokenStream => _tokenStreamController.stream;

  // Current FCM token
  String? _currentToken;
  String? get currentToken => _currentToken;

  // Notification settings
  bool _notificationsEnabled = true;
  bool _orderNotificationsEnabled = true;
  bool _earningsNotificationsEnabled = true;
  bool _adminNotificationsEnabled = true;

  // Initialization status
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// Initialize the push notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      developer.log('🔔 Initializing Push Notification Service...');

      // Initialize Firebase Messaging
      _messaging = FirebaseMessaging.instance;

      // Request permissions
      await _requestPermissions();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Set up message handlers
      await _setupMessageHandlers();

      // Get and save FCM token
      await _setupFCMToken();

      // Load notification settings
      await _loadNotificationSettings();

      _isInitialized = true;
      developer.log('✅ Push Notification Service initialized successfully');
    } catch (e) {
      developer.log('❌ Failed to initialize Push Notification Service: $e');
      rethrow;
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    if (_messaging == null) return;

    try {
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      developer.log(
        'Notification permission status: ${settings.authorizationStatus}',
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        developer.log('✅ Notification permissions granted');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        developer.log('⚠️ Provisional notification permissions granted');
      } else {
        developer.log('❌ Notification permissions denied');
      }
    } catch (e) {
      developer.log('Error requesting permissions: $e');
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    try {
      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      // Initialize the plugin
      await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      developer.log('✅ Local notifications initialized');
    } catch (e) {
      developer.log('Error initializing local notifications: $e');
    }
  }

  /// Set up Firebase message handlers
  Future<void> _setupMessageHandlers() async {
    if (_messaging == null) return;

    try {
      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // Handle initial message if app was opened from notification
      RemoteMessage? initialMessage = await _messaging!.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }

      developer.log('✅ Message handlers set up');
    } catch (e) {
      developer.log('Error setting up message handlers: $e');
    }
  }

  /// Set up FCM token management
  Future<void> _setupFCMToken() async {
    if (_messaging == null) return;

    try {
      // Get initial token
      _currentToken = await _messaging!.getToken();
      if (_currentToken != null) {
        developer.log('FCM Token: $_currentToken');
        await _saveTokenToFirestore(_currentToken!);
        _tokenStreamController.add(_currentToken!);
      }

      // Listen for token refresh
      _messaging!.onTokenRefresh.listen((String token) {
        developer.log('FCM Token refreshed: $token');
        _currentToken = token;
        _saveTokenToFirestore(token);
        _tokenStreamController.add(token);
      });
    } catch (e) {
      developer.log('Error setting up FCM token: $e');
    }
  }

  /// Save FCM token to Firestore for the current user
  Future<void> _saveTokenToFirestore(String token) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
          'fcmToken': token,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        developer.log('✅ FCM token saved to Firestore');
      }
    } catch (e) {
      developer.log('Error saving FCM token to Firestore: $e');
    }
  }

  /// Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    developer.log('Received foreground message: ${message.messageId}');

    // Add to message stream
    _messageStreamController.add(message);

    // Show local notification for foreground messages
    _showLocalNotification(message);
  }

  /// Handle background messages
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    developer.log('Handling background message: ${message.messageId}');

    // Process the message (save to local storage, update app state, etc.)
    await _processNotificationData(message);
  }

  /// Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    developer.log('Notification tapped: ${message.messageId}');

    // Add to message stream
    _messageStreamController.add(message);

    // Navigate based on notification data
    _handleNotificationNavigation(message);
  }

  /// Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    developer.log('Local notification tapped: ${response.id}');

    // Parse payload and handle navigation
    if (response.payload != null) {
      try {
        final data = json.decode(response.payload!);
        _handleNavigationFromData(data);
      } catch (e) {
        developer.log('Error parsing notification payload: $e');
      }
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      // Check if notifications are enabled
      if (!_notificationsEnabled) return;

      // Check specific notification type settings
      final notificationType = message.data['type'] ?? '';
      if (!_shouldShowNotification(notificationType)) return;

      // Create notification details
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'dropshipping_channel',
            'Dropshipping Notifications',
            channelDescription: 'Notifications for dropshipping app',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show notification
      await _localNotifications.show(
        message.hashCode,
        message.notification?.title ?? 'Dropshipping App',
        message.notification?.body ?? 'You have a new notification',
        notificationDetails,
        payload: json.encode(message.data),
      );
    } catch (e) {
      developer.log('Error showing local notification: $e');
    }
  }

  /// Check if notification should be shown based on type and settings
  bool _shouldShowNotification(String type) {
    switch (type) {
      case 'order':
        return _orderNotificationsEnabled;
      case 'earnings':
        return _earningsNotificationsEnabled;
      case 'admin':
        return _adminNotificationsEnabled;
      default:
        return _notificationsEnabled;
    }
  }

  /// Process notification data
  Future<void> _processNotificationData(RemoteMessage message) async {
    try {
      final data = message.data;
      final type = data['type'] ?? '';

      switch (type) {
        case 'order':
          await _processOrderNotification(data);
          break;
        case 'earnings':
          await _processEarningsNotification(data);
          break;
        case 'admin':
          await _processAdminNotification(data);
          break;
        default:
          developer.log('Unknown notification type: $type');
      }
    } catch (e) {
      developer.log('Error processing notification data: $e');
    }
  }

  /// Process order notification
  Future<void> _processOrderNotification(Map<String, dynamic> data) async {
    // Handle order status updates
    final orderId = data['orderId'];
    final status = data['status'];

    developer.log(
      'Processing order notification - Order: $orderId, Status: $status',
    );

    // You can add logic here to update local order status, refresh UI, etc.
  }

  /// Process earnings notification
  Future<void> _processEarningsNotification(Map<String, dynamic> data) async {
    // Handle earnings updates
    final amount = data['amount'];
    final type = data['earningsType']; // 'incoming' or 'available'

    developer.log(
      'Processing earnings notification - Amount: $amount, Type: $type',
    );

    // You can add logic here to update local earnings data, refresh UI, etc.
  }

  /// Process admin notification
  Future<void> _processAdminNotification(Map<String, dynamic> data) async {
    // Handle admin alerts
    final alertType = data['alertType'];
    final message = data['message'];

    developer.log(
      'Processing admin notification - Type: $alertType, Message: $message',
    );

    // You can add logic here to handle admin alerts
  }

  /// Handle notification navigation
  void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;
    _handleNavigationFromData(data);
  }

  /// Handle navigation from notification data
  void _handleNavigationFromData(Map<String, dynamic> data) {
    final type = data['type'] ?? '';
    final route = data['route'] ?? '';

    developer.log('Handling navigation - Type: $type, Route: $route');

    // You can implement navigation logic here based on your app's routing
    // For example:
    // - Navigate to orders page for order notifications
    // - Navigate to earnings page for earnings notifications
    // - Navigate to admin panel for admin notifications
  }

  /// Send order status notification
  Future<void> sendOrderStatusNotification({
    required String userId,
    required String orderId,
    required String status,
    required String message,
  }) async {
    try {
      // Get user's FCM token
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();

      final fcmToken = userDoc.data()?['fcmToken'] as String?;
      if (fcmToken == null) {
        developer.log('No FCM token found for user: $userId');
        return;
      }

      // Send notification via Firebase Functions or your backend
      await _sendNotificationToToken(
        token: fcmToken,
        title: 'Order Update',
        body: message,
        data: {
          'type': 'order',
          'orderId': orderId,
          'status': status,
          'route': '/orders',
        },
      );
    } catch (e) {
      developer.log('Error sending order status notification: $e');
    }
  }

  /// Send earnings notification
  Future<void> sendEarningsNotification({
    required String userId,
    required double amount,
    required String earningsType,
    required String message,
  }) async {
    try {
      // Get user's FCM token
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();

      final fcmToken = userDoc.data()?['fcmToken'] as String?;
      if (fcmToken == null) {
        developer.log('No FCM token found for user: $userId');
        return;
      }

      // Send notification via Firebase Functions or your backend
      await _sendNotificationToToken(
        token: fcmToken,
        title: 'Earnings Update',
        body: message,
        data: {
          'type': 'earnings',
          'amount': amount.toString(),
          'earningsType': earningsType,
          'route': '/account',
        },
      );
    } catch (e) {
      developer.log('Error sending earnings notification: $e');
    }
  }

  /// Send admin alert notification
  Future<void> sendAdminAlert({
    required String alertType,
    required String message,
    List<String>? adminUserIds,
  }) async {
    try {
      // If no specific admin IDs provided, get all admin users
      List<String> targetUserIds = adminUserIds ?? [];

      if (targetUserIds.isEmpty) {
        // Query for admin users
        final adminQuery =
            await FirebaseFirestore.instance
                .collection('users')
                .where('isAdmin', isEqualTo: true)
                .get();

        targetUserIds = adminQuery.docs.map((doc) => doc.id).toList();
      }

      // Send notification to each admin
      for (final userId in targetUserIds) {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(userId)
                .get();

        final fcmToken = userDoc.data()?['fcmToken'] as String?;
        if (fcmToken != null) {
          await _sendNotificationToToken(
            token: fcmToken,
            title: 'Admin Alert',
            body: message,
            data: {
              'type': 'admin',
              'alertType': alertType,
              'message': message,
              'route': '/adminpanel',
            },
          );
        }
      }
    } catch (e) {
      developer.log('Error sending admin alert: $e');
    }
  }

  /// Send notification to specific FCM token
  Future<void> _sendNotificationToToken({
    required String token,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Note: In a real app, you would send this via your backend server
      // or Firebase Functions. This is a placeholder for the API call.

      developer.log('Sending notification to token: $token');
      developer.log('Title: $title, Body: $body, Data: $data');

      // You would implement the actual HTTP request to FCM here
      // or call your backend API that handles FCM notifications
    } catch (e) {
      developer.log('Error sending notification to token: $e');
    }
  }

  /// Load notification settings from SharedPreferences
  Future<void> _loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _orderNotificationsEnabled =
          prefs.getBool('order_notifications_enabled') ?? true;
      _earningsNotificationsEnabled =
          prefs.getBool('earnings_notifications_enabled') ?? true;
      _adminNotificationsEnabled =
          prefs.getBool('admin_notifications_enabled') ?? true;

      developer.log('Notification settings loaded');
    } catch (e) {
      developer.log('Error loading notification settings: $e');
    }
  }

  /// Update notification settings
  Future<void> updateNotificationSettings({
    bool? notificationsEnabled,
    bool? orderNotificationsEnabled,
    bool? earningsNotificationsEnabled,
    bool? adminNotificationsEnabled,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (notificationsEnabled != null) {
        _notificationsEnabled = notificationsEnabled;
        await prefs.setBool('notifications_enabled', notificationsEnabled);
      }

      if (orderNotificationsEnabled != null) {
        _orderNotificationsEnabled = orderNotificationsEnabled;
        await prefs.setBool(
          'order_notifications_enabled',
          orderNotificationsEnabled,
        );
      }

      if (earningsNotificationsEnabled != null) {
        _earningsNotificationsEnabled = earningsNotificationsEnabled;
        await prefs.setBool(
          'earnings_notifications_enabled',
          earningsNotificationsEnabled,
        );
      }

      if (adminNotificationsEnabled != null) {
        _adminNotificationsEnabled = adminNotificationsEnabled;
        await prefs.setBool(
          'admin_notifications_enabled',
          adminNotificationsEnabled,
        );
      }

      developer.log('Notification settings updated');
    } catch (e) {
      developer.log('Error updating notification settings: $e');
    }
  }

  /// Get current notification settings
  Map<String, bool> getNotificationSettings() {
    return {
      'notificationsEnabled': _notificationsEnabled,
      'orderNotificationsEnabled': _orderNotificationsEnabled,
      'earningsNotificationsEnabled': _earningsNotificationsEnabled,
      'adminNotificationsEnabled': _adminNotificationsEnabled,
    };
  }

  /// Subscribe to topic for broadcast notifications
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _messaging?.subscribeToTopic(topic);
      developer.log('Subscribed to topic: $topic');
    } catch (e) {
      developer.log('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _messaging?.unsubscribeFromTopic(topic);
      developer.log('Unsubscribed from topic: $topic');
    } catch (e) {
      developer.log('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _messageStreamController.close();
    _tokenStreamController.close();
  }
}
