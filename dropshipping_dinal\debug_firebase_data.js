// Debug Firebase Data - Check specific order and earnings flow
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();

// Your user ID (from the Flutter app logs)
const USER_ID = 'H4yisnMzvENCdpRT6rSHmdn7R4t1';

async function debugSpecificOrder() {
  console.log('🔍 DEBUGGING SPECIFIC ORDER EARNINGS FLOW');
  console.log('==========================================\n');

  try {
    console.log(`👤 Debugging user: ${USER_ID}\n`);

    // Step 1: Find delivered orders
    console.log('📦 STEP 1: Finding delivered orders...\n');
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', USER_ID)
      .get();

    console.log(`📊 Total orders found: ${ordersSnapshot.size}`);

    let deliveredOrderFound = false;
    for (const orderDoc of ordersSnapshot.docs) {
      const orderData = orderDoc.data();
      const orderId = orderDoc.id;
      const status = orderData.status || 'unknown';
      const totalAmount = orderData.totalAmount || 0.0;
      const totalEarnings = orderData.totalEarnings || 0.0;
      const earningsConfirmed = orderData.earningsConfirmed || false;

      console.log(`📋 Order: ${orderId.substring(0, 8)}...`);
      console.log(`   Status: ${status}`);
      console.log(`   Total Amount: $${totalAmount.toFixed(2)}`);
      console.log(`   Total Earnings: $${totalEarnings.toFixed(2)}`);
      console.log(`   Earnings Confirmed: ${earningsConfirmed}`);

      if (status.toLowerCase() === 'delivered' || status === 'Delivered') {
        deliveredOrderFound = true;
        console.log('   🚚 THIS IS A DELIVERED ORDER!');
        
        if (!earningsConfirmed && totalEarnings > 0) {
          console.log('   ⚠️  ISSUE: Earnings not confirmed despite delivery!');
          console.log('   💡 This order should trigger earnings transfer');
          
          // Check earnings record
          await checkOrderEarningsRecord(orderId, totalEarnings);
        } else if (earningsConfirmed) {
          console.log('   ✅ Earnings already confirmed for this order');
        }
      }
      console.log('');
    }

    if (!deliveredOrderFound) {
      console.log('⚠️  No delivered orders found!');
      console.log('💡 This might be why earnings aren\'t transferring');
    }

    // Step 2: Check user balance
    console.log('💰 STEP 2: Checking user balance...\n');
    await checkUserBalance();

    // Step 3: Check earnings records
    console.log('📈 STEP 3: Checking earnings records...\n');
    await checkEarningsRecords();

    // Step 4: Provide recommendations
    console.log('🔧 STEP 4: Recommendations...\n');
    await provideRecommendations();

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  }
}

async function checkOrderEarningsRecord(orderId, expectedAmount) {
  try {
    const earningsSnapshot = await db.collection('earnings')
      .where('orderId', '==', orderId)
      .where('userId', '==', USER_ID)
      .get();

    if (earningsSnapshot.empty) {
      console.log('   ❌ NO EARNINGS RECORD FOUND for this order!');
      console.log('   💡 This is why earnings aren\'t transferring');
    } else {
      const earningsData = earningsSnapshot.docs[0].data();
      console.log('   📈 Earnings record found:');
      console.log(`      Amount: $${earningsData.amount?.toFixed(2) || '0.00'}`);
      console.log(`      Status: ${earningsData.status || 'unknown'}`);
      console.log(`      Order Status: ${earningsData.orderStatus || 'unknown'}`);
    }
  } catch (error) {
    console.log('   ❌ Error checking earnings record:', error);
  }
}

async function checkUserBalance() {
  try {
    const balanceDoc = await db.collection('userBalances').doc(USER_ID).get();

    if (!balanceDoc.exists) {
      console.log('❌ NO USER BALANCE DOCUMENT FOUND!');
      console.log('💡 This is a critical issue - userBalances document missing');
      return;
    }

    const balanceData = balanceDoc.data();
    console.log('📊 User Balance Status:');
    console.log(`   Available Balance: $${balanceData.availableBalance?.toFixed(2) || '0.00'}`);
    console.log(`   Incoming Earnings: $${balanceData.incomingEarnings?.toFixed(2) || '0.00'}`);
    console.log(`   Total Earnings: $${balanceData.totalEarnings?.toFixed(2) || '0.00'}`);
    console.log(`   Last Updated: ${balanceData.updatedAt?.toDate?.() || 'Unknown'}`);
    console.log('');

    const incomingEarnings = balanceData.incomingEarnings || 0.0;
    if (incomingEarnings > 0) {
      console.log(`⚠️  ISSUE: You have $${incomingEarnings.toFixed(2)} in incoming earnings`);
      console.log('💡 These should move to available balance when orders are delivered');
    }
  } catch (error) {
    console.log('❌ Error checking user balance:', error);
  }
}

async function checkEarningsRecords() {
  try {
    const earningsSnapshot = await db.collection('earnings')
      .where('userId', '==', USER_ID)
      .get();

    console.log(`📊 Total earnings records: ${earningsSnapshot.size}`);

    for (const earningsDoc of earningsSnapshot.docs) {
      const earningsData = earningsDoc.data();
      const orderId = earningsData.orderId || 'unknown';
      const amount = earningsData.amount || 0.0;
      const status = earningsData.status || 'unknown';
      const orderStatus = earningsData.orderStatus || 'unknown';

      console.log(`📋 Earnings Record:`);
      console.log(`   Order: ${orderId.substring(0, 8)}...`);
      console.log(`   Amount: $${amount.toFixed(2)}`);
      console.log(`   Status: ${status}`);
      console.log(`   Order Status: ${orderStatus}`);

      if (orderStatus.toLowerCase() === 'delivered' && status !== 'confirmed') {
        console.log('   ⚠️  ISSUE: Order delivered but earnings not confirmed!');
      }
      console.log('');
    }
  } catch (error) {
    console.log('❌ Error checking earnings records:', error);
  }
}

async function provideRecommendations() {
  try {
    // Get delivered orders that need earnings confirmation
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', USER_ID)
      .where('status', 'in', ['Delivered', 'delivered'])
      .get();

    const unconfirmedOrders = [];
    for (const orderDoc of ordersSnapshot.docs) {
      const orderData = orderDoc.data();
      if (!orderData.earningsConfirmed && orderData.totalEarnings > 0) {
        unconfirmedOrders.push({
          id: orderDoc.id,
          amount: orderData.totalEarnings
        });
      }
    }

    if (unconfirmedOrders.length > 0) {
      console.log('🔧 MANUAL FIX NEEDED:');
      console.log(`   Found ${unconfirmedOrders.length} delivered orders with unconfirmed earnings:`);
      
      for (const order of unconfirmedOrders) {
        console.log(`   - Order ${order.id.substring(0, 8)}...: $${order.amount.toFixed(2)}`);
      }
      
      console.log('\n💡 RECOMMENDED ACTIONS:');
      console.log('1. Run the migration script to fix existing data');
      console.log('2. Check if admin panel is calling the unified earnings API');
      console.log('3. Verify OrderBalanceSyncService is working in Flutter app');
      console.log('4. Test the complete earnings flow');
      
      console.log('\n🚀 QUICK FIX COMMANDS:');
      console.log('   cd "C:\\Users\\<USER>\\OneDrive\\Desktop\\admin panel final"');
      console.log('   node sync-user-balances.js');
    } else {
      console.log('✅ No delivered orders found that need earnings confirmation');
      console.log('💡 The issue might be in the admin panel integration or sync service');
    }
  } catch (error) {
    console.log('❌ Error providing recommendations:', error);
  }
}

// Run the debug
debugSpecificOrder().then(() => {
  console.log('\n🎯 Debug completed!');
  process.exit(0);
}).catch(console.error);
