const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Firebase Build Fix Script');
console.log('============================');

console.log('📦 Building Flutter web app...');
try {
    execSync('flutter build web', { stdio: 'inherit' });
} catch (error) {
    console.error('❌ Flutter build failed:', error.message);
    process.exit(1);
}

const buildIndexPath = path.join('build', 'web', 'index.html');

if (!fs.existsSync(buildIndexPath)) {
    console.error(`❌ Error: Built index.html not found at ${buildIndexPath}`);
    process.exit(1);
}

console.log('🔥 Adding Firebase scripts to built index.html...');

// Read the current content
let content = fs.readFileSync(buildIndexPath, 'utf8');

// Define the Firebase scripts to inject
const firebaseScripts = `

  <!-- Firebase SDK Scripts - CRITICAL for Firebase to work on web -->
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-storage.js"></script>

  <!-- Firebase Configuration Script -->
  <script>
    // Firebase configuration for dropshipping dinal
    const firebaseConfig = {
      apiKey: "AIzaSyC-z8lo2VETMz93qEnONSaHth3e9d2Ux9I",
      authDomain: "dropshippingdinal-vq5iag.firebaseapp.com",
      projectId: "dropshippingdinal-vq5iag",
      storageBucket: "dropshippingdinal-vq5iag.firebasestorage.app",
      messagingSenderId: "686495930941",
      appId: "1:686495930941:web:76ed7f7128907c4f856854"
    };

    // Initialize Firebase BEFORE Flutter app starts
    try {
      console.log('🔥 Initializing Firebase from HTML...');
      firebase.initializeApp(firebaseConfig);
      console.log('✅ Firebase initialized successfully from HTML');

      // Mark Firebase as ready for Flutter
      window.firebaseReady = true;

      // Verify Firebase services are accessible
      if (firebase.auth && firebase.firestore) {
        console.log('✅ Firebase Auth and Firestore services available');
      } else {
        console.warn('⚠️ Some Firebase services may not be available');
      }
    } catch (error) {
      console.error('❌ Firebase initialization failed in HTML:', error);
      window.firebaseReady = false;
      window.firebaseError = error.message;
    }
  </script>

`;

// Replace the manifest line with manifest + Firebase scripts
const manifestLine = '  <link rel="manifest" href="manifest.json">';
const newContent = content.replace(manifestLine, manifestLine + firebaseScripts);

// Write the updated content back
fs.writeFileSync(buildIndexPath, newContent, 'utf8');

console.log(`✅ Firebase scripts successfully added to ${buildIndexPath}`);
console.log('🚀 Build complete! Your app is ready with Firebase support.');
console.log('');
console.log('📍 To serve the app locally, run:');
console.log('   flutter run -d web-server --web-port=8080');
console.log('');
