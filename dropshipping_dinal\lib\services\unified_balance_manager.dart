import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import '../auth/firebase_auth/auth_util.dart'; // Not needed

/// UNIFIED BALANCE MANAGER
/// Solves ALL balance persistence and earnings flow issues
/// Ensures balance never disappears and earnings flow correctly
class UnifiedBalanceManager extends ChangeNotifier {
  static final UnifiedBalanceManager _instance =
      UnifiedBalanceManager._internal();
  factory UnifiedBalanceManager() => _instance;
  UnifiedBalanceManager._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Balance state
  double _availableBalance = 0.0;
  double _incomingEarnings = 0.0;
  double _pendingWithdrawals = 0.0;
  bool _isLoading = false;
  String? _currentUserId;

  // Streams and listeners
  StreamSubscription<User?>? _authSubscription;
  StreamSubscription<DocumentSnapshot>? _balanceSubscription;
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  Timer? _syncTimer;

  // Getters
  double get availableBalance => _availableBalance;
  double get incomingEarnings => _incomingEarnings;
  double get pendingWithdrawals => _pendingWithdrawals;
  double get totalBalance => _availableBalance + _incomingEarnings;
  bool get isLoading => _isLoading;
  String? get currentUserId => _currentUserId;

  /// INITIALIZE THE UNIFIED BALANCE MANAGER
  Future<void> initialize() async {
    debugPrint('🚀 Initializing Unified Balance Manager...');

    // Set up auth listener to handle sign in/out
    _setupAuthListener();

    // Set up periodic sync (every 30 seconds)
    _setupPeriodicSync();

    // Initialize for current user if logged in
    final currentUser = _auth.currentUser;
    if (currentUser != null) {
      await _initializeForUser(currentUser.uid);
    }

    debugPrint('✅ Unified Balance Manager initialized');
  }

  /// SETUP AUTH LISTENER - CRITICAL FOR BALANCE PERSISTENCE
  void _setupAuthListener() {
    _authSubscription = _auth.authStateChanges().listen((User? user) async {
      debugPrint('🔄 Auth state changed: ${user?.uid}');

      if (user != null) {
        // User signed in - initialize balance
        await _initializeForUser(user.uid);
      } else {
        // User signed out - clear local data but keep cache
        await _handleUserSignOut();
      }
    });
  }

  /// INITIALIZE FOR SPECIFIC USER
  Future<void> _initializeForUser(String userId) async {
    if (_currentUserId == userId) return; // Already initialized

    _isLoading = true;
    notifyListeners();

    try {
      _currentUserId = userId;

      // Cancel previous subscriptions
      await _cancelSubscriptions();

      // Load balance from multiple sources with priority
      await _loadBalanceFromAllSources(userId);

      // Set up real-time listeners
      _setupBalanceListener(userId);
      _setupOrdersListener(userId);

      debugPrint('✅ Balance initialized for user: $userId');
      debugPrint('💰 Available: \$${_availableBalance.toStringAsFixed(2)}');
      debugPrint('📈 Incoming: \$${_incomingEarnings.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('❌ Error initializing balance for user $userId: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// LOAD BALANCE FROM ALL SOURCES (Firebase + Local Cache)
  Future<void> _loadBalanceFromAllSources(String userId) async {
    try {
      // 1. Try to load from Firebase first (most accurate)
      final firebaseBalance = await _loadBalanceFromFirebase(userId);

      if (firebaseBalance != null) {
        _updateBalanceState(firebaseBalance);
        // Save to local cache for offline access
        await _saveBalanceToLocal(userId, firebaseBalance);
        return;
      }

      // 2. Fallback to local cache if Firebase fails
      final localBalance = await _loadBalanceFromLocal(userId);
      if (localBalance != null) {
        _updateBalanceState(localBalance);
        return;
      }

      // 3. Initialize with zero balance if nothing found
      _updateBalanceState({
        'availableBalance': 0.0,
        'incomingEarnings': 0.0,
        'pendingWithdrawals': 0.0,
      });
    } catch (e) {
      debugPrint('❌ Error loading balance: $e');
    }
  }

  /// LOAD BALANCE FROM FIREBASE
  Future<Map<String, double>?> _loadBalanceFromFirebase(String userId) async {
    try {
      debugPrint('🔄 Loading balance from Firebase for user: $userId');
      final doc = await _firestore.collection('userBalances').doc(userId).get();

      if (doc.exists) {
        final data = doc.data()!;
        final balance = <String, double>{
          'availableBalance': (data['availableBalance'] ?? 0.0).toDouble(),
          'incomingEarnings': (data['incomingEarnings'] ?? 0.0).toDouble(),
          'pendingWithdrawals': (data['pendingWithdrawals'] ?? 0.0).toDouble(),
        };

        debugPrint('📊 Firebase balance loaded:');
        debugPrint(
            '   Available: \$${balance['availableBalance']!.toStringAsFixed(2)}');
        debugPrint(
            '   Incoming: \$${balance['incomingEarnings']!.toStringAsFixed(2)}');
        debugPrint(
            '   Pending: \$${balance['pendingWithdrawals']!.toStringAsFixed(2)}');

        return balance;
      } else {
        debugPrint(
            '⚠️ No balance document found in Firebase for user: $userId');
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error loading balance from Firebase: $e');
      return null;
    }
  }

  /// SAVE BALANCE TO LOCAL CACHE
  Future<void> _saveBalanceToLocal(
      String userId, Map<String, double> balance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(
          '${userId}_availableBalance', balance['availableBalance']!);
      await prefs.setDouble(
          '${userId}_incomingEarnings', balance['incomingEarnings']!);
      await prefs.setDouble(
          '${userId}_pendingWithdrawals', balance['pendingWithdrawals']!);
      await prefs.setString(
          '${userId}_lastSync', DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('❌ Error saving balance to local: $e');
    }
  }

  /// LOAD BALANCE FROM LOCAL CACHE
  Future<Map<String, double>?> _loadBalanceFromLocal(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (prefs.containsKey('${userId}_availableBalance')) {
        return {
          'availableBalance':
              prefs.getDouble('${userId}_availableBalance') ?? 0.0,
          'incomingEarnings':
              prefs.getDouble('${userId}_incomingEarnings') ?? 0.0,
          'pendingWithdrawals':
              prefs.getDouble('${userId}_pendingWithdrawals') ?? 0.0,
        };
      }

      return null;
    } catch (e) {
      debugPrint('❌ Error loading balance from local: $e');
      return null;
    }
  }

  /// SETUP REAL-TIME BALANCE LISTENER
  void _setupBalanceListener(String userId) {
    debugPrint('🔄 Setting up real-time balance listener for user: $userId');
    _balanceSubscription = _firestore
        .collection('userBalances')
        .doc(userId)
        .snapshots()
        .listen((snapshot) {
      debugPrint('📡 Real-time balance update received');
      if (snapshot.exists) {
        final data = snapshot.data()!;
        final balance = <String, double>{
          'availableBalance': (data['availableBalance'] ?? 0.0).toDouble(),
          'incomingEarnings': (data['incomingEarnings'] ?? 0.0).toDouble(),
          'pendingWithdrawals': (data['pendingWithdrawals'] ?? 0.0).toDouble(),
        };

        debugPrint('📊 Real-time balance update:');
        debugPrint(
            '   Available: \$${balance['availableBalance']!.toStringAsFixed(2)}');
        debugPrint(
            '   Incoming: \$${balance['incomingEarnings']!.toStringAsFixed(2)}');
        debugPrint(
            '   Pending: \$${balance['pendingWithdrawals']!.toStringAsFixed(2)}');

        _updateBalanceState(balance);
        _saveBalanceToLocal(userId, balance);
      } else {
        debugPrint('⚠️ Real-time listener: No balance document exists');
      }
    });
  }

  /// SETUP ORDERS LISTENER FOR AUTOMATIC EARNINGS PROCESSING
  void _setupOrdersListener(String userId) {
    _ordersSubscription = _firestore
        .collection('orders')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .listen((snapshot) {
      _processOrderUpdates(snapshot.docs);
    });
  }

  /// PROCESS ORDER UPDATES FOR EARNINGS
  Future<void> _processOrderUpdates(List<QueryDocumentSnapshot> orders) async {
    try {
      for (final orderDoc in orders) {
        final orderData = orderDoc.data() as Map<String, dynamic>;
        final orderId = orderDoc.id;
        final status = orderData['status'] as String;
        final earningsConfirmed =
            orderData['earningsConfirmed'] as bool? ?? false;
        final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();

        // If order is delivered and earnings not confirmed yet
        if (status == 'delivered' && !earningsConfirmed && totalEarnings > 0) {
          await _confirmOrderEarnings(orderId, totalEarnings);
        }
      }
    } catch (e) {
      debugPrint('❌ Error processing order updates: $e');
    }
  }

  /// CONFIRM ORDER EARNINGS (Move from incoming to available)
  Future<void> _confirmOrderEarnings(String orderId, double earnings) async {
    try {
      final userId = _currentUserId;
      if (userId == null) return;

      await _firestore.runTransaction((transaction) async {
        // Update order to mark earnings as confirmed
        final orderRef = _firestore.collection('orders').doc(orderId);
        transaction.update(orderRef, {
          'earningsConfirmed': true,
          'earningsConfirmedAt': FieldValue.serverTimestamp(),
        });

        // Update user balance
        final balanceRef = _firestore.collection('userBalances').doc(userId);
        transaction.update(balanceRef, {
          'availableBalance': FieldValue.increment(earnings),
          'incomingEarnings': FieldValue.increment(-earnings),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      debugPrint(
          '✅ Earnings confirmed for order $orderId: \$${earnings.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('❌ Error confirming earnings: $e');
    }
  }

  /// ADD INCOMING EARNINGS (When order is placed)
  Future<void> addIncomingEarnings(double amount) async {
    if (amount <= 0 || _currentUserId == null) return;

    try {
      // Only update incoming earnings and total earnings, preserve availableBalance
      await _firestore.collection('userBalances').doc(_currentUserId!).set({
        'userId': _currentUserId!,
        'incomingEarnings': FieldValue.increment(amount),
        'totalEarnings': FieldValue.increment(amount),
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // Update local state
      _incomingEarnings += amount;
      notifyListeners();

      debugPrint('✅ Added incoming earnings: \$${amount.toStringAsFixed(2)}');
      debugPrint(
          '💰 Current balance - Available: \$${_availableBalance.toStringAsFixed(2)}, Incoming: \$${_incomingEarnings.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('❌ Error adding incoming earnings: $e');
    }
  }

  /// PROCESS WITHDRAWAL
  Future<bool> processWithdrawal(double amount) async {
    if (amount <= 0 || amount > _availableBalance || _currentUserId == null) {
      return false;
    }

    try {
      await _firestore.runTransaction((transaction) async {
        final balanceRef =
            _firestore.collection('userBalances').doc(_currentUserId!);

        // Check balance again in transaction
        final balanceDoc = await transaction.get(balanceRef);
        final currentAvailable =
            (balanceDoc.data()?['availableBalance'] ?? 0.0).toDouble();

        if (currentAvailable < amount) {
          throw Exception('Insufficient balance');
        }

        // Update balance
        transaction.update(balanceRef, {
          'availableBalance': FieldValue.increment(-amount),
          'pendingWithdrawals': FieldValue.increment(amount),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Create withdrawal record
        final withdrawalRef = _firestore.collection('withdrawals').doc();
        transaction.set(withdrawalRef, {
          'id': withdrawalRef.id,
          'userId': _currentUserId!,
          'amount': amount,
          'status': 'pending',
          'createdAt': FieldValue.serverTimestamp(),
        });
      });

      debugPrint('✅ Withdrawal processed: \$${amount.toStringAsFixed(2)}');
      return true;
    } catch (e) {
      debugPrint('❌ Error processing withdrawal: $e');
      return false;
    }
  }

  /// SETUP PERIODIC SYNC
  void _setupPeriodicSync() {
    _syncTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      if (_currentUserId != null) {
        _syncWithFirebase();
      }
    });
  }

  /// SYNC WITH FIREBASE
  Future<void> _syncWithFirebase() async {
    if (_currentUserId == null) return;

    try {
      final balance = await _loadBalanceFromFirebase(_currentUserId!);
      if (balance != null) {
        _updateBalanceState(balance);
        await _saveBalanceToLocal(_currentUserId!, balance);
      }
    } catch (e) {
      debugPrint('❌ Error syncing with Firebase: $e');
    }
  }

  /// UPDATE BALANCE STATE
  void _updateBalanceState(Map<String, double> balance) {
    _availableBalance = balance['availableBalance']!;
    _incomingEarnings = balance['incomingEarnings']!;
    _pendingWithdrawals = balance['pendingWithdrawals']!;
    notifyListeners();
  }

  /// HANDLE USER SIGN OUT
  Future<void> _handleUserSignOut() async {
    debugPrint('🔄 Handling user sign out...');

    // Cancel subscriptions
    await _cancelSubscriptions();

    // Clear current user but keep local cache for next sign in
    _currentUserId = null;

    // Reset balance state
    _availableBalance = 0.0;
    _incomingEarnings = 0.0;
    _pendingWithdrawals = 0.0;

    notifyListeners();
    debugPrint('✅ User sign out handled');
  }

  /// CANCEL SUBSCRIPTIONS
  Future<void> _cancelSubscriptions() async {
    await _balanceSubscription?.cancel();
    await _ordersSubscription?.cancel();
    _balanceSubscription = null;
    _ordersSubscription = null;
  }

  /// FORCE REFRESH
  Future<void> forceRefresh() async {
    if (_currentUserId != null) {
      await _loadBalanceFromAllSources(_currentUserId!);
    }
  }

  /// GET FORMATTED BALANCES
  String get formattedAvailableBalance =>
      '\$${_availableBalance.toStringAsFixed(2)}';
  String get formattedIncomingEarnings =>
      '\$${_incomingEarnings.toStringAsFixed(2)}';
  String get formattedTotalBalance => '\$${totalBalance.toStringAsFixed(2)}';

  @override
  void dispose() {
    _authSubscription?.cancel();
    _cancelSubscriptions();
    _syncTimer?.cancel();
    super.dispose();
  }
}

// Global instance
final unifiedBalanceManager = UnifiedBalanceManager();
