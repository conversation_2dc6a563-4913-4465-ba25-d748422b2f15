#!/bin/bash

echo "🧪 Running Complete Earnings Flow Test..."
echo "========================================"

# Navigate to the project directory
cd "$(dirname "$0")"

# Run the test script
dart run test_scripts/test_earnings_flow.dart

echo ""
echo "========================================"
echo "✅ Test completed!"
echo ""
echo "📋 What this test verified:"
echo "  1. Order creation with earnings"
echo "  2. Initial balance state (incoming earnings)"
echo "  3. Admin delivery confirmation"
echo "  4. Automatic earnings transfer to available balance"
echo "  5. Manual sync functionality"
echo ""
echo "🎯 If all tests passed, your earnings flow is working correctly!"
echo "   Users will see their earnings move from 'incoming' to 'available'"
echo "   when orders are marked as 'delivered' from the admin panel."
