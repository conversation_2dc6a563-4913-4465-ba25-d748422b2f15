import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling app localization and language management
/// Supports Arabic, English, and Kurdish languages with RTL support
class LocalizationService {
  static final LocalizationService _instance = LocalizationService._internal();
  static LocalizationService get instance => _instance;
  
  LocalizationService._internal();
  
  // Factory constructor for backward compatibility
  factory LocalizationService() => _instance;
  
  bool _isInitialized = false;
  Locale _currentLocale = const Locale('en');
  TextDirection _textDirection = TextDirection.ltr;
  
  /// Supported languages with their configurations
  static const Map<String, Map<String, String>> _supportedLanguages = {
    'en': {
      'name': 'English',
      'nativeName': 'English',
      'flag': '🇺🇸',
      'isRTL': 'false',
    },
    'ar': {
      'name': 'Arabic',
      'nativeName': 'العربية',
      'flag': '🇸🇦',
      'isRTL': 'true',
    },
    'ku': {
      'name': 'Kurdish',
      'nativeName': 'کوردی',
      'flag': '🏴',
      'isRTL': 'true',
    },
  };
  
  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;
  
  /// Get current locale
  Locale get currentLocale => _currentLocale;
  
  /// Get current text direction
  TextDirection get textDirection => _textDirection;
  
  /// Get current language code
  String get currentLanguageCode => _currentLocale.languageCode;
  
  /// Check if current language is RTL
  bool get isRTL => _textDirection == TextDirection.rtl;
  
  /// Initialize the localization service
  Future<void> initialize() async {
    try {
      developer.log('🌐 Initializing Localization Service...');
      
      // Load saved language preference
      await _loadSavedLanguage();
      
      // Set text direction based on language
      _updateTextDirection();
      
      _isInitialized = true;
      developer.log('✅ Localization Service initialized - Language: ${_currentLocale.languageCode}');
    } catch (e) {
      developer.log('❌ Localization Service initialization failed: $e');
      // Set default values and continue
      _currentLocale = const Locale('en');
      _textDirection = TextDirection.ltr;
      _isInitialized = true;
    }
  }
  
  /// Load saved language preference from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguageCode = prefs.getString('selected_language') ?? 'en';
      
      if (_supportedLanguages.containsKey(savedLanguageCode)) {
        _currentLocale = Locale(savedLanguageCode);
        developer.log('📱 Loaded saved language: $savedLanguageCode');
      } else {
        developer.log('⚠️ Invalid saved language: $savedLanguageCode, using default');
        _currentLocale = const Locale('en');
      }
    } catch (e) {
      developer.log('⚠️ Failed to load saved language: $e');
      _currentLocale = const Locale('en');
    }
  }
  
  /// Update text direction based on current language
  void _updateTextDirection() {
    final languageConfig = _supportedLanguages[_currentLocale.languageCode];
    final isRTL = languageConfig?['isRTL'] == 'true';
    _textDirection = isRTL ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Change the app language
  Future<void> changeLanguage(String languageCode) async {
    try {
      if (!_supportedLanguages.containsKey(languageCode)) {
        developer.log('❌ Unsupported language: $languageCode');
        return;
      }
      
      if (_currentLocale.languageCode == languageCode) {
        developer.log('ℹ️ Language already set to: $languageCode');
        return;
      }
      
      // Update current locale
      _currentLocale = Locale(languageCode);
      _updateTextDirection();
      
      // Save preference
      await _saveLanguagePreference(languageCode);
      
      developer.log('🔄 Language changed to: $languageCode (RTL: $isRTL)');
    } catch (e) {
      developer.log('❌ Failed to change language: $e');
    }
  }
  
  /// Save language preference to SharedPreferences
  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_language', languageCode);
      developer.log('💾 Language preference saved: $languageCode');
    } catch (e) {
      developer.log('⚠️ Failed to save language preference: $e');
    }
  }
  
  /// Get list of available languages
  List<Map<String, String>> getAvailableLanguages() {
    return _supportedLanguages.entries.map((entry) {
      return {
        'code': entry.key,
        ...entry.value,
      };
    }).toList();
  }
  
  /// Get language name by code
  String getLanguageName(String languageCode) {
    return _supportedLanguages[languageCode]?['name'] ?? languageCode;
  }
  
  /// Get native language name by code
  String getNativeLanguageName(String languageCode) {
    return _supportedLanguages[languageCode]?['nativeName'] ?? languageCode;
  }
  
  /// Get language flag emoji by code
  String getLanguageFlag(String languageCode) {
    return _supportedLanguages[languageCode]?['flag'] ?? '🏳️';
  }
  
  /// Check if a language is RTL
  bool isLanguageRTL(String languageCode) {
    return _supportedLanguages[languageCode]?['isRTL'] == 'true';
  }
  
  /// Get text direction for a specific language
  TextDirection getTextDirectionForLanguage(String languageCode) {
    return isLanguageRTL(languageCode) ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Get localized text (placeholder implementation)
  String getText(String key, {Map<String, String>? params}) {
    // This is a simplified implementation
    // In a real app, this would load from language files
    final texts = _getLocalizedTexts(_currentLocale.languageCode);
    String text = texts[key] ?? key;
    
    // Replace parameters if provided
    if (params != null) {
      params.forEach((paramKey, paramValue) {
        text = text.replaceAll('{$paramKey}', paramValue);
      });
    }
    
    return text;
  }
  
  /// Get localized texts for a language (simplified implementation)
  Map<String, String> _getLocalizedTexts(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return {
          'app_title': 'تطبيق دروبشيبينغ',
          'welcome': 'مرحباً',
          'orders': 'الطلبات',
          'products': 'المنتجات',
          'cart': 'السلة',
          'settings': 'الإعدادات',
          'language': 'اللغة',
          'logout': 'تسجيل الخروج',
        };
      case 'ku':
        return {
          'app_title': 'ئەپی دروپشیپینگ',
          'welcome': 'بەخێربێیت',
          'orders': 'داواکاریەکان',
          'products': 'بەرهەمەکان',
          'cart': 'سەبەتە',
          'settings': 'ڕێکخستنەکان',
          'language': 'زمان',
          'logout': 'چوونەدەرەوە',
        };
      default: // English
        return {
          'app_title': 'Dropshipping App',
          'welcome': 'Welcome',
          'orders': 'Orders',
          'products': 'Products',
          'cart': 'Cart',
          'settings': 'Settings',
          'language': 'Language',
          'logout': 'Logout',
        };
    }
  }
  
  /// Format number according to locale
  String formatNumber(num number) {
    // Simplified number formatting
    if (_currentLocale.languageCode == 'ar' || _currentLocale.languageCode == 'ku') {
      // For Arabic and Kurdish, you might want to use Arabic-Indic numerals
      return number.toString();
    }
    return number.toString();
  }
  
  /// Format currency according to locale
  String formatCurrency(double amount) {
    // Simplified currency formatting
    switch (_currentLocale.languageCode) {
      case 'ar':
        return '$amount د.ع'; // Iraqi Dinar
      case 'ku':
        return '$amount دینار';
      default:
        return '\$$amount';
    }
  }
  
  /// Get localization settings
  Map<String, dynamic> getLocalizationSettings() {
    return {
      'current_language': _currentLocale.languageCode,
      'text_direction': _textDirection.name,
      'is_rtl': isRTL,
      'supported_languages': _supportedLanguages.keys.toList(),
    };
  }
  
  /// Reset to default language
  Future<void> resetToDefault() async {
    await changeLanguage('en');
  }
  
  /// Cleanup resources
  Future<void> dispose() async {
    try {
      _isInitialized = false;
      developer.log('🧹 Localization Service disposed');
    } catch (e) {
      developer.log('❌ Error disposing Localization Service: $e');
    }
  }
}
