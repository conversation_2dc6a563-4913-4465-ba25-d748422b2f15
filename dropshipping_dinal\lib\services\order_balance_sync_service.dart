import 'dart:async';
import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Enhanced service that handles real-time synchronization between order status changes
/// and user balance updates. This ensures that when orders are marked as "delivered"
/// from the admin panel, earnings automatically move from incoming to available balance.
class OrderBalanceSyncService extends ChangeNotifier {
  static final OrderBalanceSyncService _instance =
      OrderBalanceSyncService._internal();
  factory OrderBalanceSyncService() => _instance;
  OrderBalanceSyncService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<User?>? _authSubscription;

  String? _currentUserId;
  bool _isInitialized = false;
  bool _isProcessing = false;

  // Collections
  late CollectionReference _ordersCollection;
  late CollectionReference _userBalancesCollection;
  late CollectionReference _earningsCollection;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      developer.log('🔄 Initializing OrderBalanceSyncService...');

      // Initialize collections
      _ordersCollection = _firestore.collection('orders');
      _userBalancesCollection = _firestore.collection('userBalances');
      _earningsCollection = _firestore.collection('earnings');

      // Listen to auth changes
      _authSubscription = _auth.authStateChanges().listen(_handleAuthChange);

      _isInitialized = true;
      developer.log('✅ OrderBalanceSyncService initialized successfully');

      // If user is already signed in, set up listener immediately
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        developer.log(
            '🔄 User already signed in, setting up listener for: ${currentUser.uid}');
        _currentUserId = currentUser.uid;
        _setupOrdersListener();
      }
    } catch (e) {
      developer.log('❌ Error initializing OrderBalanceSyncService: $e');
    }
  }

  /// Handle authentication state changes
  void _handleAuthChange(User? user) {
    if (user != null && user.uid != _currentUserId) {
      _currentUserId = user.uid;
      _setupOrdersListener();
    } else if (user == null) {
      _currentUserId = null;
      _cancelOrdersListener();
    }
  }

  /// Set up real-time listener for order status changes
  void _setupOrdersListener() {
    if (_currentUserId == null) return;

    _cancelOrdersListener();

    developer.log('🔄 Setting up orders listener for user: $_currentUserId');

    _ordersSubscription = _ordersCollection
        .where('userId', isEqualTo: _currentUserId)
        .snapshots()
        .listen(
      _handleOrdersUpdate,
      onError: (error) {
        developer.log('❌ Error in orders listener: $error');
      },
    );
  }

  /// Handle order updates from Firestore
  Future<void> _handleOrdersUpdate(QuerySnapshot snapshot) async {
    if (_isProcessing || _currentUserId == null) return;

    _isProcessing = true;

    try {
      for (final change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.modified) {
          await _processOrderChange(change.doc);
        }
      }
    } catch (e) {
      developer.log('❌ Error handling orders update: $e');
    } finally {
      _isProcessing = false;
    }
  }

  /// Process individual order changes
  Future<void> _processOrderChange(DocumentSnapshot orderDoc) async {
    try {
      final orderData = orderDoc.data() as Map<String, dynamic>;
      final orderId = orderDoc.id;
      final status = orderData['status'] as String?;
      final earningsConfirmed =
          orderData['earningsConfirmed'] as bool? ?? false;
      final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();

      developer.log(
          '🔄 Processing order change: $orderId, status: $status, earnings: $totalEarnings, confirmed: $earningsConfirmed');

      // If order is delivered and earnings haven't been confirmed yet
      if ((status == 'delivered' || status == 'Delivered') &&
          !earningsConfirmed &&
          totalEarnings > 0) {
        await _moveEarningsToAvailable(orderId, totalEarnings);
      }
    } catch (e) {
      developer.log('❌ Error processing order change: $e');
    }
  }

  /// Move earnings from incoming to available balance
  Future<void> _moveEarningsToAvailable(String orderId, double earnings) async {
    if (_currentUserId == null) return;

    try {
      developer.log(
          '💰 Moving earnings to available balance: Order $orderId, Amount: \$${earnings.toStringAsFixed(2)}');

      await _firestore.runTransaction((transaction) async {
        // Update order to mark earnings as confirmed
        final orderRef = _ordersCollection.doc(orderId);
        transaction.update(orderRef, {
          'earningsConfirmed': true,
          'earningsConfirmedAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Update user balance
        final balanceRef = _userBalancesCollection.doc(_currentUserId!);
        transaction.set(
            balanceRef,
            {
              'userId': _currentUserId!,
              'availableBalance': FieldValue.increment(earnings),
              'incomingEarnings': FieldValue.increment(-earnings),
              'updatedAt': FieldValue.serverTimestamp(),
            },
            SetOptions(merge: true));

        // Update earnings record
        final earningsQuery = await _earningsCollection
            .where('orderId', isEqualTo: orderId)
            .where('userId', isEqualTo: _currentUserId!)
            .get();

        if (earningsQuery.docs.isNotEmpty) {
          final earningsRef = earningsQuery.docs.first.reference;
          transaction.update(earningsRef, {
            'status': 'confirmed',
            'orderStatus': 'delivered',
            'confirmedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } else {
          // Create earnings record if it doesn't exist
          final newEarningsRef = _earningsCollection.doc();
          transaction.set(newEarningsRef, {
            'userId': _currentUserId!,
            'orderId': orderId,
            'amount': earnings,
            'status': 'confirmed',
            'orderStatus': 'delivered',
            'createdAt': FieldValue.serverTimestamp(),
            'confirmedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      });

      developer.log(
          '✅ Successfully moved earnings to available balance: \$${earnings.toStringAsFixed(2)}');
      notifyListeners(); // Notify listeners about the change
    } catch (e) {
      developer.log('❌ Error moving earnings to available balance: $e');
      rethrow;
    }
  }

  /// Manual sync method for force-refreshing balance
  Future<void> forceSyncBalance() async {
    if (_currentUserId == null) {
      developer.log('❌ Force sync failed: No current user ID');
      return;
    }

    try {
      developer.log('🔄 Force syncing balance for user: $_currentUserId');

      // Get all orders for this user first
      final allOrdersSnapshot = await _ordersCollection
          .where('userId', isEqualTo: _currentUserId!)
          .get();

      developer.log(
          '📊 Found ${allOrdersSnapshot.docs.length} total orders for user');

      // Get all delivered orders that haven't had earnings confirmed
      final ordersSnapshot = await _ordersCollection
          .where('userId', isEqualTo: _currentUserId!)
          .where('status', whereIn: ['delivered', 'Delivered']).get();

      developer.log('📦 Found ${ordersSnapshot.docs.length} delivered orders');

      for (final orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data() as Map<String, dynamic>;
        final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();
        final earningsConfirmed = orderData['earningsConfirmed'] ?? false;
        final orderId = orderDoc.id;

        developer.log(
            '📋 Order $orderId: earnings=$totalEarnings, confirmed=$earningsConfirmed');

        if (totalEarnings > 0 && !earningsConfirmed) {
          developer.log('💰 Processing earnings for order $orderId');
          await _moveEarningsToAvailable(orderId, totalEarnings);
        } else if (earningsConfirmed) {
          developer.log('✅ Order $orderId already has confirmed earnings');
        } else {
          developer.log('⚠️ Order $orderId has no earnings to process');
        }
      }

      developer.log('✅ Force sync completed');
    } catch (e) {
      developer.log('❌ Error in force sync: $e');
      rethrow;
    }
  }

  /// Cancel orders listener
  void _cancelOrdersListener() {
    _ordersSubscription?.cancel();
    _ordersSubscription = null;
  }

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if currently processing
  bool get isProcessing => _isProcessing;

  /// Dispose the service
  @override
  void dispose() {
    _cancelOrdersListener();
    _authSubscription?.cancel();
    super.dispose();
  }
}
