import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage app localization and language switching
class LocalizationService extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _rtlKey = 'is_rtl_enabled';
  
  // Singleton pattern
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();
  
  // Current locale
  Locale _currentLocale = const Locale('en');
  Locale get currentLocale => _currentLocale;
  
  // RTL support
  bool _isRTL = false;
  bool get isRTL => _isRTL;
  
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('ar'), // Arabic
    Locale('ku'), // Kurdish
  ];
  
  // Language names for display
  static const Map<String, String> languageNames = {
    'en': 'English',
    'ar': 'العربية',
    'ku': 'کوردی',
  };
  
  // RTL languages
  static const Set<String> rtlLanguages = {'ar', 'ku'};
  
  /// Initialize the localization service
  Future<void> initialize() async {
    await _loadSavedLanguage();
    await _loadRTLSetting();
  }
  
  /// Load saved language from SharedPreferences
  Future<void> _loadSavedLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString(_languageKey);
      
      if (savedLanguage != null) {
        _currentLocale = Locale(savedLanguage);
        _updateRTLStatus();
      } else {
        // Use system locale if available and supported
        final systemLocale = PlatformDispatcher.instance.locale;
        if (supportedLocales.contains(systemLocale)) {
          _currentLocale = systemLocale;
          _updateRTLStatus();
          await _saveLanguage(systemLocale.languageCode);
        }
      }
    } catch (e) {
      debugPrint('Error loading saved language: $e');
    }
  }
  
  /// Load RTL setting from SharedPreferences
  Future<void> _loadRTLSetting() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isRTL = prefs.getBool(_rtlKey) ?? _isRTLLanguage(_currentLocale.languageCode);
    } catch (e) {
      debugPrint('Error loading RTL setting: $e');
    }
  }
  
  /// Save language to SharedPreferences
  Future<void> _saveLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
    } catch (e) {
      debugPrint('Error saving language: $e');
    }
  }
  
  /// Save RTL setting to SharedPreferences
  Future<void> _saveRTLSetting(bool isRTL) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_rtlKey, isRTL);
    } catch (e) {
      debugPrint('Error saving RTL setting: $e');
    }
  }
  
  /// Change the app language
  Future<void> changeLanguage(String languageCode) async {
    if (!supportedLocales.any((locale) => locale.languageCode == languageCode)) {
      debugPrint('Unsupported language: $languageCode');
      return;
    }
    
    _currentLocale = Locale(languageCode);
    _updateRTLStatus();
    
    await _saveLanguage(languageCode);
    await _saveRTLSetting(_isRTL);
    
    notifyListeners();
  }
  
  /// Toggle RTL mode manually (for testing or user preference)
  Future<void> toggleRTL() async {
    _isRTL = !_isRTL;
    await _saveRTLSetting(_isRTL);
    notifyListeners();
  }
  
  /// Update RTL status based on current language
  void _updateRTLStatus() {
    _isRTL = _isRTLLanguage(_currentLocale.languageCode);
  }
  
  /// Check if a language is RTL
  bool _isRTLLanguage(String languageCode) {
    return rtlLanguages.contains(languageCode);
  }
  
  /// Get language name for display
  String getLanguageName(String languageCode) {
    return languageNames[languageCode] ?? languageCode;
  }
  
  /// Get current language name
  String get currentLanguageName {
    return getLanguageName(_currentLocale.languageCode);
  }
  
  /// Get text direction based on current locale
  TextDirection get textDirection {
    return _isRTL ? TextDirection.rtl : TextDirection.ltr;
  }
  
  /// Format currency with proper locale
  String formatCurrency(double amount) {
    // For Iraqi Dinar, we'll use a simple format
    // You can enhance this with proper NumberFormat if needed
    if (_currentLocale.languageCode == 'ar' || _currentLocale.languageCode == 'ku') {
      return '${amount.toStringAsFixed(0)} د.ع';
    } else {
      return '${amount.toStringAsFixed(0)} IQD';
    }
  }
  
  /// Format number with proper locale
  String formatNumber(double number) {
    // Simple number formatting - can be enhanced with NumberFormat
    return number.toStringAsFixed(0);
  }
  
  /// Get supported locales for the app
  static List<Locale> getSupportedLocales() {
    return supportedLocales;
  }
  
  /// Check if a locale is supported
  static bool isLocaleSupported(Locale locale) {
    return supportedLocales.contains(locale);
  }
  
  /// Get locale resolution callback for MaterialApp
  static Locale? localeResolutionCallback(
    List<Locale>? locales,
    Iterable<Locale> supportedLocales,
  ) {
    if (locales == null || locales.isEmpty) {
      return supportedLocales.first;
    }
    
    // Try to find exact match
    for (final locale in locales) {
      if (supportedLocales.contains(locale)) {
        return locale;
      }
    }
    
    // Try to find language match
    for (final locale in locales) {
      for (final supportedLocale in supportedLocales) {
        if (locale.languageCode == supportedLocale.languageCode) {
          return supportedLocale;
        }
      }
    }
    
    // Return default locale
    return supportedLocales.first;
  }
  
  /// Reset to default language
  Future<void> resetToDefault() async {
    await changeLanguage('en');
  }
  
  /// Get available languages for language picker
  List<Map<String, String>> getAvailableLanguages() {
    return supportedLocales.map((locale) {
      return {
        'code': locale.languageCode,
        'name': getLanguageName(locale.languageCode),
        'isRTL': _isRTLLanguage(locale.languageCode).toString(),
      };
    }).toList();
  }
}
