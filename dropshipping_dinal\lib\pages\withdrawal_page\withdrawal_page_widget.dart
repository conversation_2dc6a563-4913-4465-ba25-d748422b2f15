import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/services/withdrawal_service.dart';
import '/services/unified_balance_manager.dart';
import '/services/order_balance_sync_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class WithdrawalPageWidget extends StatefulWidget {
  const WithdrawalPageWidget({super.key});

  @override
  State<WithdrawalPageWidget> createState() => _WithdrawalPageWidgetState();
}

class _WithdrawalPageWidgetState extends State<WithdrawalPageWidget> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _accountHolderController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedPaymentMethod = 'bank_transfer';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Load user balance and withdrawal history using enhanced services
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Use UnifiedBalanceManager for persistent balance
      context.read<UnifiedBalanceManager>().forceRefresh();
      context.read<WithdrawalService>().loadWithdrawals();

      // Force sync balance to check for any delivered orders
      context.read<OrderBalanceSyncService>().forceSyncBalance();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _accountNumberController.dispose();
    _bankNameController.dispose();
    _accountHolderController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _submitWithdrawal() async {
    if (!_formKey.currentState!.validate()) return;

    final amount = double.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      _showError('Please enter a valid amount');
      return;
    }

    final withdrawalService = context.read<WithdrawalService>();

    // Validate minimum amount
    if (amount < withdrawalService.minWithdrawalAmount) {
      _showError(
          'Minimum withdrawal amount is \$${withdrawalService.minWithdrawalAmount.toStringAsFixed(2)}');
      return;
    }

    // Validate maximum amount
    if (amount > withdrawalService.maxWithdrawalAmount) {
      _showError(
          'Maximum withdrawal amount is \$${withdrawalService.maxWithdrawalAmount.toStringAsFixed(2)}');
      return;
    }

    setState(() => _isLoading = true);

    try {
      Map<String, dynamic> paymentDetails = {};

      switch (_selectedPaymentMethod) {
        case 'bank_transfer':
          paymentDetails = {
            'accountNumber': _accountNumberController.text,
            'bankName': _bankNameController.text,
            'accountHolder': _accountHolderController.text,
          };
          break;
        case 'paypal':
          paymentDetails = {
            'email': _accountNumberController.text,
          };
          break;
        case 'mobile_money':
          paymentDetails = {
            'phoneNumber': _accountNumberController.text,
            'provider': _bankNameController.text,
          };
          break;
      }

      final success = await withdrawalService.requestWithdrawal(
        amount: amount,
        paymentMethod: _selectedPaymentMethod,
        paymentDetails: paymentDetails,
        notes: _notesController.text,
      );

      if (success) {
        _showSuccess('Withdrawal request submitted successfully!');
        _clearForm();
        // Reload balance using enhanced service
        if (mounted) {
          context.read<UnifiedBalanceManager>().forceRefresh();
        }
      } else {
        _showError(
            withdrawalService.error ?? 'Failed to submit withdrawal request');
      }
    } catch (e) {
      _showError('An error occurred: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _clearForm() {
    _amountController.clear();
    _accountNumberController.clear();
    _bankNameController.clear();
    _accountHolderController.clear();
    _notesController.clear();
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: AppBar(
        backgroundColor: FlutterFlowTheme.of(context).primary,
        automaticallyImplyLeading: false,
        leading: FlutterFlowIconButton(
          borderColor: Colors.transparent,
          borderRadius: 30.0,
          borderWidth: 1.0,
          buttonSize: 60.0,
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Colors.white,
            size: 30.0,
          ),
          onPressed: () async {
            context.pop();
          },
        ),
        title: Text(
          'Withdraw Earnings',
          style: FlutterFlowTheme.of(context).headlineMedium.override(
                fontFamily: 'Inter',
                color: Colors.white,
                fontSize: 22.0,
                letterSpacing: 0.0,
              ),
        ),
        centerTitle: true,
        elevation: 2.0,
      ),
      body: SafeArea(
        top: true,
        child: Consumer2<UnifiedBalanceManager, WithdrawalService>(
          builder: (context, balanceManager, withdrawalService, child) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Balance Card
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primary,
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Text(
                          'Available Balance',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'Inter',
                                    color: Colors.white70,
                                    fontSize: 16.0,
                                  ),
                        ),
                        const SizedBox(height: 8.0),
                        Text(
                          '\$${balanceManager.availableBalance.toStringAsFixed(2)}',
                          style: FlutterFlowTheme.of(context)
                              .headlineLarge
                              .override(
                                fontFamily: 'Inter',
                                color: Colors.white,
                                fontSize: 32.0,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 16.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Column(
                              children: [
                                Text(
                                  'Incoming',
                                  style: FlutterFlowTheme.of(context)
                                      .bodySmall
                                      .override(
                                        fontFamily: 'Inter',
                                        color: Colors.white70,
                                      ),
                                ),
                                Text(
                                  '\$${balanceManager.incomingEarnings.toStringAsFixed(2)}',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Inter',
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                Text(
                                  'Pending',
                                  style: FlutterFlowTheme.of(context)
                                      .bodySmall
                                      .override(
                                        fontFamily: 'Inter',
                                        color: Colors.white70,
                                      ),
                                ),
                                Text(
                                  '\$${balanceManager.pendingWithdrawals.toStringAsFixed(2)}',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Inter',
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16.0),

                  // Sync Balance Button
                  SizedBox(
                    width: double.infinity,
                    child: FFButtonWidget(
                      onPressed: () async {
                        if (!mounted) return;

                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        final orderSyncService =
                            context.read<OrderBalanceSyncService>();
                        final balanceManager =
                            context.read<UnifiedBalanceManager>();

                        try {
                          // Show loading indicator
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(
                              content: Text('Syncing balance...'),
                              backgroundColor: Colors.blue,
                              duration: Duration(seconds: 1),
                            ),
                          );

                          // Force sync balance
                          await orderSyncService.forceSyncBalance();
                          await balanceManager.forceRefresh();

                          // Show success message
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text('Balance updated successfully!'),
                                backgroundColor: Colors.green,
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        } catch (e) {
                          // Show error message
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text('Error syncing balance: $e'),
                                backgroundColor: Colors.red,
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          }
                        }
                      },
                      text: 'Sync Balance',
                      icon: const Icon(
                        Icons.refresh,
                        size: 18.0,
                        color: Colors.white,
                      ),
                      options: FFButtonOptions(
                        height: 40.0,
                        padding: const EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 0.0),
                        iconPadding: const EdgeInsetsDirectional.fromSTEB(
                            0.0, 0.0, 0.0, 0.0),
                        color: FlutterFlowTheme.of(context).secondary,
                        textStyle:
                            FlutterFlowTheme.of(context).titleSmall.override(
                                  fontFamily: 'Inter',
                                  color: Colors.white,
                                  fontSize: 14.0,
                                  fontWeight: FontWeight.w500,
                                ),
                        elevation: 1.0,
                        borderSide: const BorderSide(
                          color: Colors.transparent,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24.0),

                  // Withdrawal Form
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Withdrawal Request',
                          style: FlutterFlowTheme.of(context)
                              .headlineSmall
                              .override(
                                fontFamily: 'Inter',
                                fontSize: 20.0,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        const SizedBox(height: 16.0),

                        // Amount Field
                        TextFormField(
                          controller: _amountController,
                          decoration: InputDecoration(
                            labelText: 'Amount (\$)',
                            hintText: 'Enter withdrawal amount',
                            prefixIcon: const Icon(Icons.attach_money),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          keyboardType:
                              TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d+\.?\d{0,2}')),
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter an amount';
                            }
                            final amount = double.tryParse(value);
                            if (amount == null || amount <= 0) {
                              return 'Please enter a valid amount';
                            }
                            if (amount > balanceManager.availableBalance) {
                              return 'Amount exceeds available balance';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16.0),

                        // Payment Method Selection
                        Text(
                          'Payment Method',
                          style:
                              FlutterFlowTheme.of(context).bodyMedium.override(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                        const SizedBox(height: 8.0),

                        DropdownButtonFormField<String>(
                          value: _selectedPaymentMethod,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          items: const [
                            DropdownMenuItem(
                              value: 'bank_transfer',
                              child: Text('Bank Transfer'),
                            ),
                            DropdownMenuItem(
                              value: 'paypal',
                              child: Text('PayPal'),
                            ),
                            DropdownMenuItem(
                              value: 'mobile_money',
                              child: Text('Mobile Money'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedPaymentMethod = value!;
                              _clearForm();
                            });
                          },
                        ),

                        const SizedBox(height: 16.0),

                        // Payment Details Fields
                        ..._buildPaymentDetailsFields(),

                        const SizedBox(height: 16.0),

                        // Notes Field
                        TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'Notes (Optional)',
                            hintText: 'Additional information',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          maxLines: 3,
                        ),

                        const SizedBox(height: 24.0),

                        // Submit Button
                        SizedBox(
                          width: double.infinity,
                          child: FFButtonWidget(
                            onPressed: _isLoading ? null : _submitWithdrawal,
                            text: _isLoading
                                ? 'Processing...'
                                : 'Submit Withdrawal Request',
                            options: FFButtonOptions(
                              height: 50.0,
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              iconPadding: const EdgeInsetsDirectional.fromSTEB(
                                  0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).primary,
                              textStyle: FlutterFlowTheme.of(context)
                                  .titleSmall
                                  .override(
                                    fontFamily: 'Inter',
                                    color: Colors.white,
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.w600,
                                  ),
                              elevation: 2.0,
                              borderSide: const BorderSide(
                                color: Colors.transparent,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32.0),

                  // Withdrawal History
                  Text(
                    'Recent Withdrawals',
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontFamily: 'Inter',
                          fontSize: 18.0,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 16.0),

                  if (withdrawalService.withdrawals.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20.0),
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).secondaryBackground,
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Text(
                        'No withdrawal requests yet',
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'Inter',
                              color: FlutterFlowTheme.of(context).secondaryText,
                            ),
                      ),
                    )
                  else
                    ...withdrawalService.withdrawals.take(5).map((withdrawal) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8.0),
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(
                            color: FlutterFlowTheme.of(context).alternate,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '\$${(withdrawal['amount'] ?? 0.0).toStringAsFixed(2)}',
                                  style: FlutterFlowTheme.of(context)
                                      .bodyLarge
                                      .override(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                                Text(
                                  withdrawalService.getPaymentMethodDisplayName(
                                      withdrawal['paymentMethod'] ?? ''),
                                  style: FlutterFlowTheme.of(context)
                                      .bodySmall
                                      .override(
                                        fontFamily: 'Inter',
                                        color: FlutterFlowTheme.of(context)
                                            .secondaryText,
                                      ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12.0, vertical: 6.0),
                              decoration: BoxDecoration(
                                color:
                                    _getStatusColor(withdrawal['status'] ?? ''),
                                borderRadius: BorderRadius.circular(16.0),
                              ),
                              child: Text(
                                withdrawalService.getStatusDisplayName(
                                    withdrawal['status'] ?? ''),
                                style: FlutterFlowTheme.of(context)
                                    .bodySmall
                                    .override(
                                      fontFamily: 'Inter',
                                      color: Colors.white,
                                      fontSize: 12.0,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildPaymentDetailsFields() {
    switch (_selectedPaymentMethod) {
      case 'bank_transfer':
        return [
          TextFormField(
            controller: _accountNumberController,
            decoration: InputDecoration(
              labelText: 'Account Number',
              hintText: 'Enter your account number',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your account number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16.0),
          TextFormField(
            controller: _bankNameController,
            decoration: InputDecoration(
              labelText: 'Bank Name',
              hintText: 'Enter your bank name',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your bank name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16.0),
          TextFormField(
            controller: _accountHolderController,
            decoration: InputDecoration(
              labelText: 'Account Holder Name',
              hintText: 'Enter account holder name',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter account holder name';
              }
              return null;
            },
          ),
        ];

      case 'paypal':
        return [
          TextFormField(
            controller: _accountNumberController,
            decoration: InputDecoration(
              labelText: 'PayPal Email',
              hintText: 'Enter your PayPal email',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your PayPal email';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email address';
              }
              return null;
            },
          ),
        ];

      case 'mobile_money':
        return [
          TextFormField(
            controller: _accountNumberController,
            decoration: InputDecoration(
              labelText: 'Phone Number',
              hintText: 'Enter your phone number',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16.0),
          TextFormField(
            controller: _bankNameController,
            decoration: InputDecoration(
              labelText: 'Mobile Money Provider',
              hintText: 'e.g., MTN, Vodafone, Airtel',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your mobile money provider';
              }
              return null;
            },
          ),
        ];

      default:
        return [];
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'rejected':
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
