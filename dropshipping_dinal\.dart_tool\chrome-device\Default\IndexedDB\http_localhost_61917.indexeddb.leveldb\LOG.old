2025/06/18-20:18:29.496 5a44 Creating DB C:\Users\<USER>\AppData\Local\Temp\flutter_tools.861f8e2d\flutter_tools_chrome_device.3a221932\Default\IndexedDB\http_localhost_61917.indexeddb.leveldb since it was missing.
2025/06/18-20:18:29.500 5a44 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.861f8e2d\flutter_tools_chrome_device.3a221932\Default\IndexedDB\http_localhost_61917.indexeddb.leveldb/MANIFEST-000001
2025/06/18-20:18:29.508 c1fc Level-0 table #5: started
2025/06/18-20:18:29.510 c1fc Level-0 table #5: 1264 bytes OK
2025/06/18-20:18:29.511 c1fc Delete type=0 #3
2025/06/18-20:18:29.512 c1fc Manual compaction at level-0 from '\x00\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/18-20:37:42.159 b088 Level-0 table #7: started
2025/06/18-20:37:42.162 b088 Level-0 table #7: 1886 bytes OK
2025/06/18-20:37:42.165 b088 Delete type=0 #4
2025/06/18-20:37:42.166 b088 Manual compaction at level-0 from (begin) .. (end); will stop at (end)
2025/06/18-20:37:42.166 b088 Manual compaction at level-1 from (begin) .. (end); will stop at '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x006\x008\x006\x004\x009\x005\x009\x003\x000\x009\x004\x001\x00:\x00w\x00e\x00b\x00:\x007\x006\x00e\x00d\x007\x00f\x007\x001\x002\x008\x009\x000\x007\x00c\x004\x00f\x008\x005\x006\x008\x005\x004' @ 122 : 1
2025/06/18-20:37:42.166 b088 Compacting 1@1 + 1@2 files
2025/06/18-20:37:42.169 b088 Generated table #8@1: 33 keys, 990 bytes
2025/06/18-20:37:42.169 b088 Compacted 1@1 + 1@2 files => 990 bytes
2025/06/18-20:37:42.171 b088 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/18-20:37:42.172 b088 Delete type=2 #5
2025/06/18-20:37:42.172 b088 Delete type=2 #7
2025/06/18-20:37:42.173 b088 Manual compaction at level-1 from '\x00\x03\x01\x02\x013\x00[\x00D\x00E\x00F\x00A\x00U\x00L\x00T\x00]\x00!\x001\x00:\x006\x008\x006\x004\x009\x005\x009\x003\x000\x009\x004\x001\x00:\x00w\x00e\x00b\x00:\x007\x006\x00e\x00d\x007\x00f\x007\x001\x002\x008\x009\x000\x007\x00c\x004\x00f\x008\x005\x006\x008\x005\x004' @ 122 : 1 .. (end); will stop at (end)
