// Firebase Security Monitoring Service
// This service provides comprehensive security monitoring and rate limiting

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:developer' as developer;

/// Firebase Security Service with rate limiting and monitoring
class FirebaseSecurityService {
  static final FirebaseSecurityService _instance =
      FirebaseSecurityService._internal();
  factory FirebaseSecurityService() => _instance;
  FirebaseSecurityService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Rate limiting configuration
  static const int _maxRequestsPerMinute = 60;
  static const int _maxWithdrawalsPerDay = 5;
  static const Duration _rateLimitWindow = Duration(minutes: 1);

  // Request tracking
  final Map<String, List<DateTime>> _userRequests = {};
  final Map<String, DateTime> _lastRequestTime = {};
  final Map<String, int> _suspiciousActivityCount = {};

  /// Initialize security service
  void initialize() {
    _setupSecurityMonitoring();
    developer.log('✅ Firebase Security Service initialized');
  }

  /// Setup security monitoring
  void _setupSecurityMonitoring() {
    // Monitor authentication state changes
    _auth.authStateChanges().listen((User? user) {
      if (user != null) {
        _logSecurityEvent('user_login', user.uid, {
          'email': user.email,
          'provider': user.providerData.map((p) => p.providerId).toList(),
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    });
  }

  /// Check if user is admin with enhanced validation
  Future<bool> isAdminUser(String userId) async {
    try {
      final adminDoc = await _firestore.collection('admin').doc(userId).get();

      if (!adminDoc.exists) {
        return false;
      }

      final adminData = adminDoc.data()!;

      // Check if admin is active and has valid role
      return adminData['active'] == true &&
          (adminData['role'] == 'admin' || adminData['role'] == 'super_admin');
    } catch (e) {
      developer.log('Error checking admin status: $e');
      return false;
    }
  }

  /// Check rate limits for user operations
  Future<bool> checkRateLimit(String userId, String operation) async {
    try {
      final now = DateTime.now();

      // Initialize user request tracking
      _userRequests[userId] ??= [];

      // Clean old requests (older than rate limit window)
      _userRequests[userId]!.removeWhere(
        (time) => now.difference(time) > _rateLimitWindow,
      );

      // Check current request count
      final currentRequests = _userRequests[userId]!.length;

      // Apply rate limits based on operation type
      int maxRequests;
      switch (operation) {
        case 'withdrawal':
          maxRequests = _maxWithdrawalsPerDay;
          break;
        case 'order_create':
          maxRequests = 10; // Max 10 orders per minute
          break;
        case 'product_read':
          maxRequests = _maxRequestsPerMinute;
          break;
        default:
          maxRequests = _maxRequestsPerMinute;
      }

      if (currentRequests >= maxRequests) {
        await _logSecurityEvent('rate_limit_exceeded', userId, {
          'operation': operation,
          'current_requests': currentRequests,
          'max_requests': maxRequests,
          'window': _rateLimitWindow.inMinutes,
        });

        return false;
      }

      // Add current request to tracking
      _userRequests[userId]!.add(now);
      _lastRequestTime[userId] = now;

      return true;
    } catch (e) {
      developer.log('Error checking rate limit: $e');
      return true; // Allow request on error to avoid blocking legitimate users
    }
  }

  /// Validate withdrawal request with enhanced security
  Future<bool> validateWithdrawalRequest(
      String userId, double amount, String method) async {
    try {
      // Check rate limits
      if (!await checkRateLimit(userId, 'withdrawal')) {
        return false;
      }

      // Check withdrawal amount limits
      if (amount <= 0 || amount > 10000) {
        await _logSecurityEvent('invalid_withdrawal_amount', userId, {
          'amount': amount,
          'reason': amount <= 0 ? 'negative_or_zero' : 'exceeds_limit',
        });
        return false;
      }

      // Check valid withdrawal methods
      const validMethods = ['bank_transfer', 'paypal', 'crypto'];
      if (!validMethods.contains(method)) {
        await _logSecurityEvent('invalid_withdrawal_method', userId, {
          'method': method,
          'valid_methods': validMethods,
        });
        return false;
      }

      // Check user balance
      final balanceDoc =
          await _firestore.collection('userBalances').doc(userId).get();
      if (!balanceDoc.exists) {
        await _logSecurityEvent('withdrawal_no_balance', userId, {
          'amount': amount,
        });
        return false;
      }

      final balance = balanceDoc.data()!;
      final availableBalance = (balance['availableBalance'] ?? 0).toDouble();

      if (amount > availableBalance) {
        await _logSecurityEvent('withdrawal_insufficient_balance', userId, {
          'requested_amount': amount,
          'available_balance': availableBalance,
        });
        return false;
      }

      // Check for recent withdrawals
      final recentWithdrawals = await _firestore
          .collection('withdrawals')
          .where('userId', isEqualTo: userId)
          .where('createdAt',
              isGreaterThan: DateTime.now().subtract(const Duration(days: 1)))
          .get();

      if (recentWithdrawals.docs.length >= _maxWithdrawalsPerDay) {
        await _logSecurityEvent('withdrawal_daily_limit_exceeded', userId, {
          'daily_withdrawals': recentWithdrawals.docs.length,
          'max_daily': _maxWithdrawalsPerDay,
        });
        return false;
      }

      return true;
    } catch (e) {
      developer.log('Error validating withdrawal: $e');
      return false;
    }
  }

  /// Validate order creation with security checks
  Future<bool> validateOrderCreation(
      String userId, Map<String, dynamic> orderData) async {
    try {
      // Check rate limits
      if (!await checkRateLimit(userId, 'order_create')) {
        return false;
      }

      // Validate required fields
      final requiredFields = ['userId', 'items', 'totalAmount', 'status'];
      for (final field in requiredFields) {
        if (!orderData.containsKey(field) || orderData[field] == null) {
          await _logSecurityEvent('invalid_order_data', userId, {
            'missing_field': field,
            'provided_fields': orderData.keys.toList(),
          });
          return false;
        }
      }

      // Validate order amount
      final totalAmount = (orderData['totalAmount'] ?? 0).toDouble();
      if (totalAmount <= 0 || totalAmount > 50000) {
        await _logSecurityEvent('invalid_order_amount', userId, {
          'amount': totalAmount,
          'reason': totalAmount <= 0 ? 'negative_or_zero' : 'exceeds_limit',
        });
        return false;
      }

      // Validate items
      final items = orderData['items'] as List?;
      if (items == null || items.isEmpty || items.length > 50) {
        await _logSecurityEvent('invalid_order_items', userId, {
          'items_count': items?.length ?? 0,
          'reason': items == null || items.isEmpty ? 'empty' : 'too_many',
        });
        return false;
      }

      // Check for suspicious order patterns
      final recentOrders = await _firestore
          .collection('orders')
          .where('userId', isEqualTo: userId)
          .where('createdAt',
              isGreaterThan: DateTime.now().subtract(const Duration(hours: 1)))
          .get();

      if (recentOrders.docs.length > 10) {
        await _logSecurityEvent('suspicious_order_frequency', userId, {
          'orders_last_hour': recentOrders.docs.length,
          'threshold': 10,
        });

        // Increment suspicious activity counter
        _suspiciousActivityCount[userId] =
            (_suspiciousActivityCount[userId] ?? 0) + 1;

        if (_suspiciousActivityCount[userId]! > 3) {
          await _flagUserForReview(userId, 'excessive_order_creation');
          return false;
        }
      }

      return true;
    } catch (e) {
      developer.log('Error validating order creation: $e');
      return false;
    }
  }

  /// Monitor and detect suspicious activities
  Future<void> detectSuspiciousActivity(
      String userId, String operation, Map<String, dynamic> context) async {
    try {
      final now = DateTime.now();
      final lastRequest = _lastRequestTime[userId];

      // Check for rapid successive requests
      if (lastRequest != null &&
          now.difference(lastRequest).inMilliseconds < 100) {
        await _logSecurityEvent('rapid_requests', userId, {
          'operation': operation,
          'time_between_requests_ms':
              now.difference(lastRequest).inMilliseconds,
          'context': context,
        });

        _suspiciousActivityCount[userId] =
            (_suspiciousActivityCount[userId] ?? 0) + 1;
      }

      // Check for unusual patterns
      if (_userRequests[userId] != null && _userRequests[userId]!.length > 50) {
        await _logSecurityEvent('high_request_volume', userId, {
          'operation': operation,
          'requests_in_window': _userRequests[userId]!.length,
          'window_minutes': _rateLimitWindow.inMinutes,
        });
      }

      // Auto-flag users with excessive suspicious activity
      if (_suspiciousActivityCount[userId] != null &&
          _suspiciousActivityCount[userId]! > 5) {
        await _flagUserForReview(userId, 'multiple_suspicious_activities');
      }
    } catch (e) {
      developer.log('Error detecting suspicious activity: $e');
    }
  }

  /// Log security events for monitoring
  Future<void> _logSecurityEvent(
      String eventType, String userId, Map<String, dynamic> details) async {
    try {
      await _firestore.collection('securityLogs').add({
        'eventType': eventType,
        'userId': userId,
        'details': details,
        'timestamp': FieldValue.serverTimestamp(),
        'userAgent': kIsWeb ? 'web' : 'mobile',
        'severity': _getEventSeverity(eventType),
      });

      // Log to console in debug mode
      if (kDebugMode) {
        developer.log('🔒 Security Event: $eventType for user $userId');
      }
    } catch (e) {
      developer.log('Error logging security event: $e');
    }
  }

  /// Flag user for manual review
  Future<void> _flagUserForReview(String userId, String reason) async {
    try {
      await _firestore.collection('userFlags').doc(userId).set({
        'userId': userId,
        'reason': reason,
        'flaggedAt': FieldValue.serverTimestamp(),
        'status': 'pending_review',
        'suspiciousActivityCount': _suspiciousActivityCount[userId] ?? 0,
        'autoFlagged': true,
      });

      developer.log('🚨 User flagged for review: $userId - $reason');
    } catch (e) {
      developer.log('Error flagging user: $e');
    }
  }

  /// Get event severity level
  String _getEventSeverity(String eventType) {
    const highSeverityEvents = [
      'rate_limit_exceeded',
      'suspicious_order_frequency',
      'withdrawal_daily_limit_exceeded',
      'rapid_requests',
    ];

    const mediumSeverityEvents = [
      'invalid_withdrawal_amount',
      'invalid_order_amount',
      'high_request_volume',
    ];

    if (highSeverityEvents.contains(eventType)) {
      return 'high';
    } else if (mediumSeverityEvents.contains(eventType)) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /// Get security statistics
  Future<Map<String, dynamic>> getSecurityStatistics() async {
    try {
      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));

      // Get security logs from last 24 hours
      final securityLogs = await _firestore
          .collection('securityLogs')
          .where('timestamp', isGreaterThan: last24Hours)
          .get();

      // Get flagged users
      final flaggedUsers = await _firestore
          .collection('userFlags')
          .where('status', isEqualTo: 'pending_review')
          .get();

      // Analyze events by type
      final eventCounts = <String, int>{};
      final severityCounts = <String, int>{};

      for (final doc in securityLogs.docs) {
        final data = doc.data();
        final eventType = data['eventType'] as String;
        final severity = data['severity'] as String;

        eventCounts[eventType] = (eventCounts[eventType] ?? 0) + 1;
        severityCounts[severity] = (severityCounts[severity] ?? 0) + 1;
      }

      return {
        'period': '24_hours',
        'total_events': securityLogs.docs.length,
        'flagged_users': flaggedUsers.docs.length,
        'events_by_type': eventCounts,
        'events_by_severity': severityCounts,
        'active_rate_limits': _userRequests.length,
        'suspicious_users': _suspiciousActivityCount.length,
      };
    } catch (e) {
      developer.log('Error getting security statistics: $e');
      return {};
    }
  }

  /// Clear rate limits for user (admin function)
  Future<void> clearUserRateLimits(String userId) async {
    _userRequests.remove(userId);
    _lastRequestTime.remove(userId);
    _suspiciousActivityCount.remove(userId);

    developer.log('Rate limits cleared for user: $userId');
  }

  /// Reset all rate limits (admin function)
  void resetAllRateLimits() {
    _userRequests.clear();
    _lastRequestTime.clear();
    _suspiciousActivityCount.clear();

    developer.log('All rate limits reset');
  }
}
