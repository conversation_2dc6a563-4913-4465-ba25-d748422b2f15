import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import '../firebase_options.dart';

/// Comprehensive test script for the complete earnings flow
/// Tests: Order Creation → Admin Delivery Confirmation → Balance Update
void main() async {
  print('🧪 Starting Complete Earnings Flow Test...');
  
  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    final firestore = FirebaseFirestore.instance;
    final auth = FirebaseAuth.instance;
    
    // Test user credentials (use a test account)
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';
    
    print('📧 Signing in test user...');
    UserCredential userCredential;
    try {
      userCredential = await auth.signInWithEmailAndPassword(
        email: testEmail,
        password: testPassword,
      );
    } catch (e) {
      // Create test user if doesn't exist
      print('👤 Creating test user...');
      userCredential = await auth.createUserWithEmailAndPassword(
        email: testEmail,
        password: testPassword,
      );
    }
    
    final userId = userCredential.user!.uid;
    print('✅ Test user signed in: $userId');
    
    // Step 1: Create a test order with earnings
    print('\n📦 Step 1: Creating test order...');
    final orderId = await createTestOrder(firestore, userId);
    print('✅ Test order created: $orderId');
    
    // Step 2: Verify initial balance state
    print('\n💰 Step 2: Checking initial balance state...');
    await verifyInitialBalance(firestore, userId);
    
    // Step 3: Simulate admin marking order as delivered
    print('\n🚚 Step 3: Simulating admin delivery confirmation...');
    await simulateAdminDeliveryConfirmation(firestore, orderId);
    
    // Step 4: Verify final balance state
    print('\n✅ Step 4: Verifying final balance state...');
    await verifyFinalBalance(firestore, userId, orderId);
    
    // Step 5: Test manual sync functionality
    print('\n🔄 Step 5: Testing manual sync...');
    await testManualSync(firestore, userId);
    
    print('\n🎉 All tests completed successfully!');
    print('✅ Earnings flow is working correctly');
    
  } catch (e, stackTrace) {
    print('❌ Test failed: $e');
    print('Stack trace: $stackTrace');
  }
}

/// Create a test order with earnings
Future<String> createTestOrder(FirebaseFirestore firestore, String userId) async {
  final orderRef = firestore.collection('orders').doc();
  
  final orderData = {
    'id': orderRef.id,
    'userId': userId,
    'userName': 'Test User',
    'userPhone': '+1234567890',
    'address': '123 Test Street',
    'city': 'Test City',
    'items': [
      {
        'productId': 'test-product-1',
        'productName': 'Test Product',
        'productImage': 'https://example.com/image.jpg',
        'price': 25.0,
        'mainPrice': 20.0,
        'quantity': 2,
        'color': 'Red',
        'earnings': 10.0, // (25-20) * 2 = 10
      }
    ],
    'totalAmount': 50.0,
    'totalEarnings': 10.0,
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
    'status': 'pending',
    'earningsConfirmed': false,
  };
  
  await orderRef.set(orderData);
  
  // Create earnings record
  await firestore.collection('earnings').add({
    'userId': userId,
    'orderId': orderRef.id,
    'amount': 10.0,
    'status': 'pending',
    'orderStatus': 'pending',
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  });
  
  // Update user balance with incoming earnings
  await firestore.collection('userBalances').doc(userId).set({
    'userId': userId,
    'availableBalance': 0.0,
    'incomingEarnings': FieldValue.increment(10.0),
    'totalEarnings': FieldValue.increment(10.0),
    'pendingWithdrawals': 0.0,
    'totalWithdrawn': 0.0,
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  }, SetOptions(merge: true));
  
  return orderRef.id;
}

/// Verify initial balance state (incoming earnings should be 10.0, available should be 0.0)
Future<void> verifyInitialBalance(FirebaseFirestore firestore, String userId) async {
  final balanceDoc = await firestore.collection('userBalances').doc(userId).get();
  
  if (!balanceDoc.exists) {
    throw Exception('Balance document does not exist');
  }
  
  final balanceData = balanceDoc.data()!;
  final incomingEarnings = (balanceData['incomingEarnings'] ?? 0.0).toDouble();
  final availableBalance = (balanceData['availableBalance'] ?? 0.0).toDouble();
  
  print('💰 Initial Balance State:');
  print('   Incoming Earnings: \$${incomingEarnings.toStringAsFixed(2)}');
  print('   Available Balance: \$${availableBalance.toStringAsFixed(2)}');
  
  if (incomingEarnings < 10.0) {
    throw Exception('Expected incoming earnings to be at least \$10.00, got \$${incomingEarnings.toStringAsFixed(2)}');
  }
  
  print('✅ Initial balance state verified');
}

/// Simulate admin marking order as delivered
Future<void> simulateAdminDeliveryConfirmation(FirebaseFirestore firestore, String orderId) async {
  await firestore.collection('orders').doc(orderId).update({
    'status': 'delivered',
    'updatedAt': FieldValue.serverTimestamp(),
  });
  
  print('✅ Order marked as delivered');
  
  // Wait a moment for the sync service to process
  await Future.delayed(const Duration(seconds: 2));
}

/// Verify final balance state (earnings should move from incoming to available)
Future<void> verifyFinalBalance(FirebaseFirestore firestore, String userId, String orderId) async {
  // Wait a bit more for processing
  await Future.delayed(const Duration(seconds: 3));
  
  final balanceDoc = await firestore.collection('userBalances').doc(userId).get();
  final orderDoc = await firestore.collection('orders').doc(orderId).get();
  
  if (!balanceDoc.exists) {
    throw Exception('Balance document does not exist');
  }
  
  final balanceData = balanceDoc.data()!;
  final orderData = orderDoc.data()!;
  
  final incomingEarnings = (balanceData['incomingEarnings'] ?? 0.0).toDouble();
  final availableBalance = (balanceData['availableBalance'] ?? 0.0).toDouble();
  final earningsConfirmed = orderData['earningsConfirmed'] ?? false;
  
  print('💰 Final Balance State:');
  print('   Incoming Earnings: \$${incomingEarnings.toStringAsFixed(2)}');
  print('   Available Balance: \$${availableBalance.toStringAsFixed(2)}');
  print('   Order Earnings Confirmed: $earningsConfirmed');
  
  // Check if earnings were moved to available balance
  if (availableBalance < 10.0) {
    print('⚠️  Earnings not yet moved to available balance');
    print('   This might indicate the sync service needs manual triggering');
    
    // Try manual sync by updating earnings records
    await manualEarningsSync(firestore, userId, orderId);
    
    // Check again
    final updatedBalanceDoc = await firestore.collection('userBalances').doc(userId).get();
    final updatedBalanceData = updatedBalanceDoc.data()!;
    final updatedAvailableBalance = (updatedBalanceData['availableBalance'] ?? 0.0).toDouble();
    
    if (updatedAvailableBalance >= 10.0) {
      print('✅ Manual sync successful - earnings moved to available balance');
    } else {
      throw Exception('Manual sync failed - earnings still not in available balance');
    }
  } else {
    print('✅ Earnings successfully moved to available balance');
  }
  
  if (!earningsConfirmed) {
    print('⚠️  Order earnings not marked as confirmed');
  }
}

/// Manual earnings sync (simulates the OrderBalanceSyncService)
Future<void> manualEarningsSync(FirebaseFirestore firestore, String userId, String orderId) async {
  print('🔄 Performing manual earnings sync...');
  
  await firestore.runTransaction((transaction) async {
    // Update order to mark earnings as confirmed
    final orderRef = firestore.collection('orders').doc(orderId);
    transaction.update(orderRef, {
      'earningsConfirmed': true,
      'earningsConfirmedAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    
    // Update user balance
    final balanceRef = firestore.collection('userBalances').doc(userId);
    transaction.update(balanceRef, {
      'availableBalance': FieldValue.increment(10.0),
      'incomingEarnings': FieldValue.increment(-10.0),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    
    // Update earnings record
    final earningsSnapshot = await firestore.collection('earnings')
        .where('orderId', isEqualTo: orderId)
        .where('userId', isEqualTo: userId)
        .get();
    
    if (earningsSnapshot.docs.isNotEmpty) {
      final earningsRef = earningsSnapshot.docs.first.reference;
      transaction.update(earningsRef, {
        'status': 'confirmed',
        'orderStatus': 'delivered',
        'confirmedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
  });
}

/// Test manual sync functionality
Future<void> testManualSync(FirebaseFirestore firestore, String userId) async {
  print('🔄 Testing manual sync functionality...');
  
  // Get all delivered orders that haven't had earnings confirmed
  final ordersSnapshot = await firestore.collection('orders')
      .where('userId', isEqualTo: userId)
      .where('status', isEqualTo: 'delivered')
      .where('earningsConfirmed', isEqualTo: false)
      .get();
  
  print('📊 Found ${ordersSnapshot.docs.length} delivered orders with unconfirmed earnings');
  
  for (final orderDoc in ordersSnapshot.docs) {
    final orderData = orderDoc.data();
    final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();
    
    if (totalEarnings > 0) {
      await manualEarningsSync(firestore, userId, orderDoc.id);
      print('✅ Synced earnings for order: ${orderDoc.id}');
    }
  }
  
  print('✅ Manual sync test completed');
}
