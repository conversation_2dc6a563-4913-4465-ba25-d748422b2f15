import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Neepula';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get signup => 'Sign Up';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get home => 'Home';

  @override
  String get products => 'Products';

  @override
  String get cart => 'Cart';

  @override
  String get orders => 'Orders';

  @override
  String get account => 'Account';

  @override
  String get settings => 'Settings';

  @override
  String get notifications => 'Notifications';

  @override
  String get search => 'Search';

  @override
  String get addToCart => 'Add to Cart';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get price => 'Price';

  @override
  String get mainPrice => 'Main Price';

  @override
  String get minPrice => 'Min Price';

  @override
  String get maxPrice => 'Max Price';

  @override
  String get currency => 'IQD';

  @override
  String get total => 'Total';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get earnings => 'Earnings';

  @override
  String get availableBalance => 'Available Balance';

  @override
  String get incomingEarnings => 'Incoming Earnings';

  @override
  String get withdraw => 'Withdraw';

  @override
  String get orderStatus => 'Order Status';

  @override
  String get pending => 'Pending';

  @override
  String get processing => 'Processing';

  @override
  String get shipped => 'Shipped';

  @override
  String get delivered => 'Delivered';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get logout => 'Logout';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get askAI => 'Ask AI';

  @override
  String get aiResponse => 'AI Response';
}
