import 'app_localizations.dart';

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'نيبولا';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get signup => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get home => 'الرئيسية';

  @override
  String get products => 'المنتجات';

  @override
  String get cart => 'السلة';

  @override
  String get orders => 'الطلبات';

  @override
  String get account => 'الحساب';

  @override
  String get settings => 'الإعدادات';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get search => 'البحث';

  @override
  String get addToCart => 'أضف إلى السلة';

  @override
  String get buyNow => 'اشتري الآن';

  @override
  String get price => 'السعر';

  @override
  String get mainPrice => 'السعر الأساسي';

  @override
  String get minPrice => 'أقل سعر';

  @override
  String get maxPrice => 'أعلى سعر';

  @override
  String get currency => 'د.ع';

  @override
  String get total => 'المجموع';

  @override
  String get subtotal => 'المجموع الفرعي';

  @override
  String get earnings => 'الأرباح';

  @override
  String get availableBalance => 'الرصيد المتاح';

  @override
  String get incomingEarnings => 'الأرباح الواردة';

  @override
  String get withdraw => 'سحب';

  @override
  String get orderStatus => 'حالة الطلب';

  @override
  String get pending => 'في الانتظار';

  @override
  String get processing => 'قيد المعالجة';

  @override
  String get shipped => 'تم الشحن';

  @override
  String get delivered => 'تم التسليم';

  @override
  String get cancelled => 'ملغي';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get language => 'اللغة';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get aiAssistant => 'المساعد الذكي';

  @override
  String get askAI => 'اسأل الذكي';

  @override
  String get aiResponse => 'رد الذكي';
}
