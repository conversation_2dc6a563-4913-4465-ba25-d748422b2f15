import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/flutter_flow_util.dart';

/// Widget for displaying detailed order information
class OrderDetailWidget extends StatefulWidget {
  const OrderDetailWidget({
    super.key,
    this.orderId,
  });

  final String? orderId;

  static const String routeName = 'OrderDetailWidget';
  static const String routePath = '/order-detail';

  @override
  State<OrderDetailWidget> createState() => _OrderDetailWidgetState();
}

class _OrderDetailWidgetState extends State<OrderDetailWidget> {
  late Future<DocumentSnapshot> _orderFuture;
  bool _isInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    if (!_isInitialized) {
      _isInitialized = true;

      // Get order ID from route parameters or widget parameter
      final orderId = widget.orderId ??
          GoRouterState.of(context).pathParameters['orderId'] ??
          '';

      if (orderId.isNotEmpty) {
        _orderFuture =
            FirebaseFirestore.instance.collection('orders').doc(orderId).get();
      } else {
        // Create a failed future if no order ID
        _orderFuture = Future.error('No order ID provided');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: AppBar(
        backgroundColor: FlutterFlowTheme.of(context).primary,
        automaticallyImplyLeading: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: FlutterFlowTheme.of(context).info,
            size: 24,
          ),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Order Details',
          style: FlutterFlowTheme.of(context).headlineMedium.override(
                fontFamily: 'Outfit',
                color: FlutterFlowTheme.of(context).info,
                fontSize: 22,
                letterSpacing: 0,
              ),
        ),
        centerTitle: true,
        elevation: 2,
      ),
      body: FutureBuilder<DocumentSnapshot>(
        future: _orderFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: FlutterFlowTheme.of(context).error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading order details',
                    style: FlutterFlowTheme.of(context).headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    snapshot.error.toString(),
                    style: FlutterFlowTheme.of(context).bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  FFButtonWidget(
                    onPressed: () => context.pop(),
                    text: 'Go Back',
                    options: FFButtonOptions(
                      width: 200,
                      height: 40,
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
                      iconPadding:
                          const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      color: FlutterFlowTheme.of(context).primary,
                      textStyle:
                          FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'Readex Pro',
                                color: Colors.white,
                                letterSpacing: 0,
                              ),
                      elevation: 3,
                      borderSide: const BorderSide(
                        color: Colors.transparent,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData || !snapshot.data!.exists) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Order not found',
                    style: FlutterFlowTheme.of(context).headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'The order you\'re looking for doesn\'t exist.',
                    style: FlutterFlowTheme.of(context).bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  FFButtonWidget(
                    onPressed: () => context.pop(),
                    text: 'Go Back',
                    options: FFButtonOptions(
                      width: 200,
                      height: 40,
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(24, 0, 24, 0),
                      iconPadding:
                          const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                      color: FlutterFlowTheme.of(context).primary,
                      textStyle:
                          FlutterFlowTheme.of(context).titleSmall.override(
                                fontFamily: 'Readex Pro',
                                color: Colors.white,
                                letterSpacing: 0,
                              ),
                      elevation: 3,
                      borderSide: const BorderSide(
                        color: Colors.transparent,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ),
            );
          }

          // Order exists, display details
          final orderData = snapshot.data!.data() as Map<String, dynamic>;
          return _buildOrderDetails(orderData);
        },
      ),
    );
  }

  Widget _buildOrderDetails(Map<String, dynamic> orderData) {
    final orderId = widget.orderId ??
        GoRouterState.of(context).pathParameters['orderId'] ??
        'Unknown';

    final status = orderData['status'] ?? 'Unknown';
    final totalAmount = (orderData['totalAmount'] ?? 0.0).toDouble();
    final totalEarnings = (orderData['totalEarnings'] ?? 0.0).toDouble();
    final earningsConfirmed = orderData['earningsConfirmed'] ?? false;
    final userName = orderData['userName'] ?? 'Unknown User';
    final userPhone = orderData['userPhone'] ?? '';
    final address = orderData['address'] ?? '';
    final city = orderData['city'] ?? '';
    final items = orderData['items'] as List<dynamic>? ?? [];

    // Parse timestamp
    final createdAt = orderData['createdAt'] as Timestamp?;
    final createdDate = createdAt?.toDate() ?? DateTime.now();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: FlutterFlowTheme.of(context).alternate,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Order #${orderId.length > 8 ? orderId.substring(orderId.length - 8) : orderId}',
                      style: FlutterFlowTheme.of(context).headlineSmall,
                    ),
                    _buildStatusChip(status),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Placed on ${DateFormat('MMM dd, yyyy at hh:mm a').format(createdDate)}',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'Readex Pro',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        letterSpacing: 0,
                      ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Customer Information
          _buildInfoSection(
            'Customer Information',
            [
              _buildInfoRow('Name', userName),
              if (userPhone.isNotEmpty) _buildInfoRow('Phone', userPhone),
              if (address.isNotEmpty)
                _buildInfoRow('Address', '$address, $city'),
            ],
          ),

          const SizedBox(height: 16),

          // Order Items
          _buildInfoSection(
            'Order Items',
            items.map((item) => _buildOrderItem(item)).toList(),
          ),

          const SizedBox(height: 16),

          // Order Summary
          _buildInfoSection(
            'Order Summary',
            [
              _buildInfoRow(
                  'Total Amount', '\$${totalAmount.toStringAsFixed(2)}'),
              _buildInfoRow(
                  'Total Earnings', '\$${totalEarnings.toStringAsFixed(2)}'),
              _buildInfoRow(
                'Earnings Status',
                earningsConfirmed ? 'Confirmed' : 'Pending',
                valueColor: earningsConfirmed
                    ? FlutterFlowTheme.of(context).success
                    : FlutterFlowTheme.of(context).warning,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'pending':
        backgroundColor = FlutterFlowTheme.of(context).warning;
        textColor = Colors.white;
        break;
      case 'processing':
        backgroundColor = FlutterFlowTheme.of(context).info;
        textColor = Colors.white;
        break;
      case 'shipped':
        backgroundColor = FlutterFlowTheme.of(context).primary;
        textColor = Colors.white;
        break;
      case 'delivered':
        backgroundColor = FlutterFlowTheme.of(context).success;
        textColor = Colors.white;
        break;
      case 'cancelled':
        backgroundColor = FlutterFlowTheme.of(context).error;
        textColor = Colors.white;
        break;
      default:
        backgroundColor = FlutterFlowTheme.of(context).secondaryText;
        textColor = Colors.white;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.toUpperCase(),
        style: FlutterFlowTheme.of(context).bodySmall.override(
              fontFamily: 'Readex Pro',
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 0,
            ),
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FlutterFlowTheme.of(context).titleMedium,
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'Readex Pro',
                    color: FlutterFlowTheme.of(context).secondaryText,
                    letterSpacing: 0,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'Readex Pro',
                    color:
                        valueColor ?? FlutterFlowTheme.of(context).primaryText,
                    letterSpacing: 0,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItem(dynamic item) {
    final productName = item['productName'] ?? 'Unknown Product';
    final quantity = item['quantity'] ?? 1;
    final price = (item['price'] ?? 0.0).toDouble();
    final productImage = item['productImage'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).primaryBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: FlutterFlowTheme.of(context).alternate,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Product Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: productImage.isNotEmpty
                  ? DecorationImage(
                      image: NetworkImage(productImage),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: productImage.isEmpty
                  ? FlutterFlowTheme.of(context).alternate
                  : null,
            ),
            child: productImage.isEmpty
                ? Icon(
                    Icons.image,
                    color: FlutterFlowTheme.of(context).secondaryText,
                  )
                : null,
          ),
          const SizedBox(width: 12),

          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  productName,
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'Readex Pro',
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Quantity: $quantity',
                  style: FlutterFlowTheme.of(context).bodySmall.override(
                        fontFamily: 'Readex Pro',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        letterSpacing: 0,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  '\$${price.toStringAsFixed(2)}',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'Readex Pro',
                        color: FlutterFlowTheme.of(context).primary,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
