import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_ku.dart';

/// Simplified localization system for the dropshipping app
/// Supports Arabic, English, and Kurdish languages
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = locale;

  final String localeName;

  /// Get localization instance for a specific locale
  static AppLocalizations of(String locale) {
    switch (locale) {
      case 'ar':
        return AppLocalizationsAr();
      case 'ku':
        return AppLocalizationsKu();
      default:
        return AppLocalizationsEn();
    }
  }

  /// Get current instance (defaults to English)
  static AppLocalizations get current => AppLocalizationsEn();

  /// Supported language codes
  static const List<String> supportedLocales = ['ar', 'en', 'ku'];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Neepula'**
  String get appTitle;

  /// Welcome message
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// Login button text
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// Sign up button text
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// Email field label
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// Password field label
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// Forgot password link text
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// Home page title
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Products page title
  ///
  /// In en, this message translates to:
  /// **'Products'**
  String get products;

  /// Shopping cart title
  ///
  /// In en, this message translates to:
  /// **'Cart'**
  String get cart;

  /// Orders page title
  ///
  /// In en, this message translates to:
  /// **'Orders'**
  String get orders;

  /// Account page title
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// Settings page title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Notifications page title
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Search placeholder text
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Add to cart button text
  ///
  /// In en, this message translates to:
  /// **'Add to Cart'**
  String get addToCart;

  /// Buy now button text
  ///
  /// In en, this message translates to:
  /// **'Buy Now'**
  String get buyNow;

  /// Price label
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// Main price label
  ///
  /// In en, this message translates to:
  /// **'Main Price'**
  String get mainPrice;

  /// Minimum price label
  ///
  /// In en, this message translates to:
  /// **'Min Price'**
  String get minPrice;

  /// Maximum price label
  ///
  /// In en, this message translates to:
  /// **'Max Price'**
  String get maxPrice;

  /// Iraqi Dinar currency symbol
  ///
  /// In en, this message translates to:
  /// **'IQD'**
  String get currency;

  /// Total amount label
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// Subtotal amount label
  ///
  /// In en, this message translates to:
  /// **'Subtotal'**
  String get subtotal;

  /// Earnings label
  ///
  /// In en, this message translates to:
  /// **'Earnings'**
  String get earnings;

  /// Available balance label
  ///
  /// In en, this message translates to:
  /// **'Available Balance'**
  String get availableBalance;

  /// Incoming earnings label
  ///
  /// In en, this message translates to:
  /// **'Incoming Earnings'**
  String get incomingEarnings;

  /// Withdraw button text
  ///
  /// In en, this message translates to:
  /// **'Withdraw'**
  String get withdraw;

  /// Order status label
  ///
  /// In en, this message translates to:
  /// **'Order Status'**
  String get orderStatus;

  /// Pending status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Processing status
  ///
  /// In en, this message translates to:
  /// **'Processing'**
  String get processing;

  /// Shipped status
  ///
  /// In en, this message translates to:
  /// **'Shipped'**
  String get shipped;

  /// Delivered status
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get delivered;

  /// Cancelled status
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// Dark mode setting label
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// Language setting label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Logout button text
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// AI Assistant label
  ///
  /// In en, this message translates to:
  /// **'AI Assistant'**
  String get aiAssistant;

  /// Ask AI button text
  ///
  /// In en, this message translates to:
  /// **'Ask AI'**
  String get askAI;

  /// AI response label
  ///
  /// In en, this message translates to:
  /// **'AI Response'**
  String get aiResponse;
}
