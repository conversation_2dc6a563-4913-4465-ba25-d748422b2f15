@echo off
echo 🔧 Firebase Build Fix Script
echo ============================

echo 📦 Building Flutter web app...
call flutter build web

echo 🔥 Adding Firebase scripts to built index.html...

set "BUILD_INDEX=build\web\index.html"
set "TEMP_FILE=build\web\index_temp.html"

if not exist "%BUILD_INDEX%" (
    echo ❌ Error: Built index.html not found at %BUILD_INDEX%
    exit /b 1
)

echo 📝 Processing %BUILD_INDEX%...

(
    for /f "delims=" %%i in (%BUILD_INDEX%) do (
        echo %%i
        if "%%i"=="  <link rel=""manifest"" href=""manifest.json"">" (
            echo.
            echo   ^<!-- Firebase SDK Scripts - CRITICAL for Firebase to work on web --^>
            echo   ^<script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-app.js"^>^</script^>
            echo   ^<script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-auth.js"^>^</script^>
            echo   ^<script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-firestore.js"^>^</script^>
            echo   ^<script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-storage.js"^>^</script^>
            echo.
            echo   ^<!-- Firebase Configuration Script --^>
            echo   ^<script^>
            echo     // Firebase configuration for dropshipping dinal
            echo     const firebaseConfig = {
            echo       apiKey: "AIzaSyC-z8lo2VETMz93qEnONSaHth3e9d2Ux9I",
            echo       authDomain: "dropshippingdinal-vq5iag.firebaseapp.com",
            echo       projectId: "dropshippingdinal-vq5iag",
            echo       storageBucket: "dropshippingdinal-vq5iag.firebasestorage.app",
            echo       messagingSenderId: "686495930941",
            echo       appId: "1:686495930941:web:76ed7f7128907c4f856854"
            echo     };
            echo.
            echo     // Initialize Firebase BEFORE Flutter app starts
            echo     try {
            echo       console.log^('🔥 Initializing Firebase from HTML...'^);
            echo       firebase.initializeApp^(firebaseConfig^);
            echo       console.log^('✅ Firebase initialized successfully from HTML'^);
            echo.
            echo       // Mark Firebase as ready for Flutter
            echo       window.firebaseReady = true;
            echo.
            echo       // Verify Firebase services are accessible
            echo       if ^(firebase.auth ^&^& firebase.firestore^) {
            echo         console.log^('✅ Firebase Auth and Firestore services available'^);
            echo       } else {
            echo         console.warn^('⚠️ Some Firebase services may not be available'^);
            echo       }
            echo     } catch ^(error^) {
            echo       console.error^('❌ Firebase initialization failed in HTML:', error^);
            echo       window.firebaseReady = false;
            echo       window.firebaseError = error.message;
            echo     }
            echo   ^</script^>
        )
    )
) > "%TEMP_FILE%"

move "%TEMP_FILE%" "%BUILD_INDEX%"

echo ✅ Firebase scripts successfully added to %BUILD_INDEX%
echo 🚀 Build complete! Your app is ready with Firebase support.
echo.
echo 📍 To serve the app locally, run:
echo    flutter run -d web-server --web-port=8080
echo.
