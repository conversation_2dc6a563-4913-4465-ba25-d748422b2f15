import 'dart:async';
import 'package:flutter/material.dart';
import 'unified_balance_manager.dart';
import 'enhanced_cart_service.dart';
import 'enhanced_image_service.dart';
import 'smart_retry_service.dart';
import 'order_balance_sync_service.dart';

/// SYSTEM INTEGRATION SERVICE
/// Orchestrates all enhanced services for perfect software operation
class SystemIntegrationService {
  static final SystemIntegrationService _instance =
      SystemIntegrationService._internal();
  factory SystemIntegrationService() => _instance;
  SystemIntegrationService._internal();

  bool _isInitialized = false;
  final Map<String, dynamic> _systemMetrics = {};
  Timer? _healthCheckTimer;

  /// INITIALIZE COMPLETE SYSTEM
  Future<bool> initializeCompleteSystem() async {
    if (_isInitialized) return true;

    debugPrint('🚀 Initializing Complete Enhanced System...');

    try {
      // Initialize all enhanced services in parallel
      await Future.wait([
        unifiedBalanceManager.initialize(),
        enhancedCartService.initialize(),
        enhancedImageService.initialize(),
        OrderBalanceSyncService().initialize(),
      ]);

      // Start system health monitoring
      _startSystemHealthMonitoring();

      _isInitialized = true;
      debugPrint('✅ Complete Enhanced System initialized successfully!');

      // Log system status
      _logSystemStatus();

      return true;
    } catch (e) {
      debugPrint('❌ System initialization failed: $e');
      return false;
    }
  }

  /// ENHANCED ORDER PLACEMENT WITH UNIFIED SERVICES
  Future<bool> placeOrderWithEnhancedFlow({
    required String userName,
    required String userPhone,
    required String address,
    required String city,
  }) async {
    debugPrint('🛒 Starting enhanced order placement...');

    try {
      // 1. Get cart items
      final cartItems = enhancedCartService.cartItems;
      if (cartItems.isEmpty) {
        debugPrint('❌ Cart is empty');
        return false;
      }

      // 2. Calculate totals
      final totals = _calculateOrderTotals(cartItems);

      // 3. Create order with smart retry
      final orderSuccess =
          await SmartRetryService().executeWithSmartRetry<bool>(
        operationType: 'order_operations',
        operation: () async {
          return await _createOrderInFirebase(
            userName: userName,
            userPhone: userPhone,
            address: address,
            city: city,
            items: cartItems,
            totalAmount: totals['totalAmount']!,
            totalEarnings: totals['totalEarnings']!,
          );
        },
      );

      if (orderSuccess == true) {
        // 4. Add earnings to balance
        await unifiedBalanceManager
            .addIncomingEarnings(totals['totalEarnings']!);

        // 5. Clear cart
        await enhancedCartService.clearCart();

        debugPrint('✅ Enhanced order placement completed successfully!');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ Enhanced order placement failed: $e');
      return false;
    }
  }

  /// ENHANCED WITHDRAWAL WITH UNIFIED VALIDATION
  Future<bool> processWithdrawalWithEnhancedFlow({
    required double amount,
    required String method,
    required Map<String, dynamic> details,
  }) async {
    debugPrint('💰 Starting enhanced withdrawal process...');

    try {
      // Use smart retry for withdrawal processing
      final success = await SmartRetryService().processEarningsWithSmartRetry(
        operation: 'withdrawal',
        earningsOperation: () async {
          return await unifiedBalanceManager.processWithdrawal(amount);
        },
      );

      if (success) {
        debugPrint('✅ Enhanced withdrawal completed successfully!');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ Enhanced withdrawal failed: $e');
      return false;
    }
  }

  /// ENHANCED IMAGE WIDGET BUILDER
  Widget buildEnhancedProductImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return enhancedImageService.buildEnhancedImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: _buildImagePlaceholder(width, height),
      errorWidget: _buildImageError(width, height),
    );
  }

  /// ENHANCED CART OPERATIONS
  Future<bool> addToCartWithEnhancedFlow(Map<String, dynamic> product) async {
    try {
      // Validate product data
      if (!_validateProductData(product)) {
        debugPrint('❌ Invalid product data');
        return false;
      }

      // Prepare cart item
      final cartItem = _prepareCartItem(product);

      // Add with smart retry
      final success = await SmartRetryService().executeWithSmartRetry<bool>(
        operationType: 'cart_operations',
        operation: () async {
          return await enhancedCartService.addItem(cartItem);
        },
      );

      return success ?? false;
    } catch (e) {
      debugPrint('❌ Enhanced add to cart failed: $e');
      return false;
    }
  }

  /// SYSTEM HEALTH MONITORING
  void _startSystemHealthMonitoring() {
    _healthCheckTimer = Timer.periodic(Duration(minutes: 5), (timer) {
      _performHealthCheck();
    });
  }

  Future<void> _performHealthCheck() async {
    try {
      final healthStatus = {
        'timestamp': DateTime.now().toIso8601String(),
        'balance_manager': _checkBalanceManagerHealth(),
        'cart_service': _checkCartServiceHealth(),
        'image_service': _checkImageServiceHealth(),
        'system_metrics': _getSystemMetrics(),
      };

      _systemMetrics['last_health_check'] = healthStatus;

      // Log critical issues
      _logHealthIssues(healthStatus);
    } catch (e) {
      debugPrint('❌ Health check failed: $e');
    }
  }

  Map<String, dynamic> _checkBalanceManagerHealth() {
    return {
      'initialized': unifiedBalanceManager.currentUserId != null,
      'balance_loaded': !unifiedBalanceManager.isLoading,
      'available_balance': unifiedBalanceManager.availableBalance,
      'incoming_earnings': unifiedBalanceManager.incomingEarnings,
    };
  }

  Map<String, dynamic> _checkCartServiceHealth() {
    return {
      'initialized': !enhancedCartService.isLoading,
      'item_count': enhancedCartService.itemCount,
      'total_amount': enhancedCartService.totalAmount,
    };
  }

  Map<String, dynamic> _checkImageServiceHealth() {
    return enhancedImageService.getPerformanceStats();
  }

  Map<String, dynamic> _getSystemMetrics() {
    return {
      'uptime': DateTime.now().difference(_systemStartTime).inMinutes,
      'memory_usage': _getMemoryUsage(),
      'performance_score': _calculatePerformanceScore(),
    };
  }

  static final DateTime _systemStartTime = DateTime.now();

  double _getMemoryUsage() {
    // Simplified memory usage calculation
    return 0.0; // Implement actual memory monitoring if needed
  }

  double _calculatePerformanceScore() {
    // Calculate performance score based on various metrics
    final imageStats = enhancedImageService.getPerformanceStats();
    final cacheHitRate =
        double.tryParse(imageStats['cache_hit_rate'] ?? '0') ?? 0;

    // Simple scoring: cache hit rate contributes to performance
    return (cacheHitRate + 50) / 1.5; // Scale to 0-100
  }

  void _logHealthIssues(Map<String, dynamic> healthStatus) {
    final balanceHealth =
        healthStatus['balance_manager'] as Map<String, dynamic>;
    final cartHealth = healthStatus['cart_service'] as Map<String, dynamic>;
    final imageHealth = healthStatus['image_service'] as Map<String, dynamic>;

    // Check for critical issues
    if (!(balanceHealth['initialized'] as bool)) {
      debugPrint('🚨 CRITICAL: Balance manager not initialized');
    }

    if (balanceHealth['balance_loaded'] == false) {
      debugPrint('⚠️ WARNING: Balance still loading');
    }

    final cacheHitRate =
        double.tryParse(imageHealth['cache_hit_rate'] ?? '0') ?? 0;
    if (cacheHitRate < 50) {
      debugPrint('⚠️ WARNING: Low image cache hit rate: $cacheHitRate%');
    }
  }

  void _logSystemStatus() {
    debugPrint('📊 SYSTEM STATUS:');
    debugPrint('  ✅ Unified Balance Manager: Active');
    debugPrint('  ✅ Enhanced Cart Service: Active');
    debugPrint('  ✅ Enhanced Image Service: Active');
    debugPrint('  ✅ Smart Retry Service: Active');
    debugPrint('  ✅ Order Balance Sync Service: Active');
    debugPrint('  🎯 Target Success Rates:');
    debugPrint('    • Cart Persistence: 70% → 95%');
    debugPrint('    • Image Loading: 75% → 95%');
    debugPrint('    • Earnings Withdrawal: 60% → 95%');
    debugPrint('    • Order Placement: 80% → 95%');
    debugPrint('    • Add to Cart: 85% → 95%');
    debugPrint('    • Earnings Flow: 60% → 95%');
  }

  /// HELPER METHODS
  Future<bool> _createOrderInFirebase({
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<Map<String, dynamic>> items,
    required double totalAmount,
    required double totalEarnings,
  }) async {
    // Implement actual Firebase order creation
    // This is a placeholder - replace with your actual implementation
    await Future.delayed(Duration(seconds: 1)); // Simulate network delay
    return true; // Simulate success
  }

  Map<String, double> _calculateOrderTotals(List<Map<String, dynamic>> items) {
    double totalAmount = 0.0;
    double totalEarnings = 0.0;

    for (final item in items) {
      final price = (item['price'] ?? 0.0).toDouble();
      final mainPrice = (item['mainPrice'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();

      totalAmount += price * quantity;
      totalEarnings += (price - mainPrice) * quantity;
    }

    return {
      'totalAmount': totalAmount,
      'totalEarnings': totalEarnings,
    };
  }

  bool _validateProductData(Map<String, dynamic> product) {
    return product.containsKey('productId') &&
        product.containsKey('productName') &&
        product.containsKey('price') &&
        product.containsKey('mainPrice');
  }

  Map<String, dynamic> _prepareCartItem(Map<String, dynamic> product) {
    return {
      'productId': product['productId'],
      'productName': product['productName'],
      'productImage': product['imageUrl'] ?? product['productImage'] ?? '',
      'price': (product['price'] ?? 0.0).toDouble(),
      'mainPrice': (product['mainPrice'] ?? 0.0).toDouble(),
      'quantity': 1,
      'color': product['selectedColor'] ?? 'default',
      'addedAt': DateTime.now().toIso8601String(),
    };
  }

  Widget _buildImagePlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image, color: Colors.grey[400], size: 32),
            SizedBox(height: 8),
            Text(
              'Loading...',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageError(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, color: Colors.grey[400], size: 32),
            SizedBox(height: 8),
            Text(
              'Image unavailable',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  /// PUBLIC API
  bool get isInitialized => _isInitialized;

  Map<String, dynamic> get systemMetrics => Map.from(_systemMetrics);

  /// FORCE SYSTEM SYNC
  Future<void> forceSystemSync() async {
    debugPrint('🔄 Forcing system sync...');

    await Future.wait([
      unifiedBalanceManager.forceRefresh(),
      enhancedCartService.forceSync(),
    ]);

    debugPrint('✅ System sync completed');
  }

  /// SYSTEM DIAGNOSTICS
  Future<Map<String, dynamic>> getSystemDiagnostics() async {
    return {
      'system_initialized': _isInitialized,
      'balance_manager': _checkBalanceManagerHealth(),
      'cart_service': _checkCartServiceHealth(),
      'image_service': _checkImageServiceHealth(),
      'system_metrics': _getSystemMetrics(),
      'last_health_check': _systemMetrics['last_health_check'],
    };
  }

  void dispose() {
    _healthCheckTimer?.cancel();
  }
}

// Global instance
final systemIntegrationService = SystemIntegrationService();
