import 'app_localizations.dart';

/// The translations for Kurdish (`ku`).
class AppLocalizationsKu extends AppLocalizations {
  AppLocalizationsKu([String locale = 'ku']) : super(locale);

  @override
  String get appTitle => 'نیپولا';

  @override
  String get welcome => 'بەخێربێیت';

  @override
  String get login => 'چوونە ژوورەوە';

  @override
  String get signup => 'هەژمارێک دروستبکە';

  @override
  String get email => 'ئیمەیڵ';

  @override
  String get password => 'وشەی نهێنی';

  @override
  String get forgotPassword => 'وشەی نهێنیت لەبیرچووە؟';

  @override
  String get home => 'سەرەکی';

  @override
  String get products => 'بەرهەمەکان';

  @override
  String get cart => 'سەبەتە';

  @override
  String get orders => 'داواکاریەکان';

  @override
  String get account => 'هەژمار';

  @override
  String get settings => 'ڕێکخستنەکان';

  @override
  String get notifications => 'ئاگادارکردنەوەکان';

  @override
  String get search => 'گەڕان';

  @override
  String get addToCart => 'زیادکردن بۆ سەبەتە';

  @override
  String get buyNow => 'ئێستا بیکڕە';

  @override
  String get price => 'نرخ';

  @override
  String get mainPrice => 'نرخی سەرەکی';

  @override
  String get minPrice => 'کەمترین نرخ';

  @override
  String get maxPrice => 'زۆرترین نرخ';

  @override
  String get currency => 'د.ع';

  @override
  String get total => 'کۆی گشتی';

  @override
  String get subtotal => 'کۆی لاوەکی';

  @override
  String get earnings => 'قازانجەکان';

  @override
  String get availableBalance => 'بڕی بەردەست';

  @override
  String get incomingEarnings => 'قازانجی داهاتوو';

  @override
  String get withdraw => 'دەرهێنان';

  @override
  String get orderStatus => 'دۆخی داواکاری';

  @override
  String get pending => 'چاوەڕوانی';

  @override
  String get processing => 'لە پرۆسەدا';

  @override
  String get shipped => 'نێردراوە';

  @override
  String get delivered => 'گەیشتووە';

  @override
  String get cancelled => 'هەڵوەشاندراوە';

  @override
  String get darkMode => 'دۆخی تاریک';

  @override
  String get language => 'زمان';

  @override
  String get logout => 'دەرچوون';

  @override
  String get aiAssistant => 'یاریدەدەری زیرەک';

  @override
  String get askAI => 'لە زیرەک بپرسە';

  @override
  String get aiResponse => 'وەڵامی زیرەک';
}
