// Enhanced Loading State Manager
// This service provides comprehensive loading state management with advanced features

import 'package:flutter/material.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../utils/loading_helper.dart';

/// Enhanced loading state manager with global state management
class EnhancedLoadingService extends ChangeNotifier {
  static final EnhancedLoadingService _instance =
      EnhancedLoadingService._internal();
  factory EnhancedLoadingService() => _instance;
  EnhancedLoadingService._internal();

  // Global loading states
  final Map<String, bool> _loadingStates = {};
  final Map<String, String> _loadingMessages = {};
  final Map<String, double> _loadingProgress = {};

  // Loading overlay management
  OverlayEntry? _currentOverlay;
  BuildContext? _overlayContext;

  /// Check if any operation is loading
  bool get isAnyLoading => _loadingStates.values.any((loading) => loading);

  /// Check if specific operation is loading
  bool isLoading(String operation) => _loadingStates[operation] ?? false;

  /// Get loading message for operation
  String? getLoadingMessage(String operation) => _loadingMessages[operation];

  /// Get loading progress for operation
  double? getLoadingProgress(String operation) => _loadingProgress[operation];

  /// Set loading state for operation
  void setLoading(String operation, bool loading,
      {String? message, double? progress}) {
    final wasLoading = _loadingStates[operation] ?? false;

    _loadingStates[operation] = loading;

    if (message != null) {
      _loadingMessages[operation] = message;
    } else if (!loading) {
      _loadingMessages.remove(operation);
    }

    if (progress != null) {
      _loadingProgress[operation] = progress;
    } else if (!loading) {
      _loadingProgress.remove(operation);
    }

    // Clean up completed operations
    if (!loading) {
      _loadingStates.remove(operation);
    }

    // Only notify if state actually changed
    if (wasLoading != loading) {
      notifyListeners();
    }
  }

  /// Execute operation with loading state
  Future<T> executeWithLoading<T>(
    String operation,
    Future<T> Function() task, {
    String? message,
    VoidCallback? onStart,
    VoidCallback? onComplete,
    Function(dynamic)? onError,
  }) async {
    try {
      setLoading(operation, true, message: message);
      onStart?.call();

      final result = await task();

      onComplete?.call();
      return result;
    } catch (error) {
      onError?.call(error);
      rethrow;
    } finally {
      setLoading(operation, false);
    }
  }

  /// Show global loading overlay
  void showGlobalLoading(
    BuildContext context, {
    String? message,
    bool dismissible = false,
    Color? backgroundColor,
  }) {
    hideGlobalLoading(); // Remove any existing overlay

    _overlayContext = context;
    _currentOverlay = OverlayEntry(
      builder: (context) => GlobalLoadingOverlay(
        message: message,
        dismissible: dismissible,
        backgroundColor: backgroundColor,
        onDismiss: dismissible ? hideGlobalLoading : null,
      ),
    );

    Overlay.of(context).insert(_currentOverlay!);
  }

  /// Hide global loading overlay
  void hideGlobalLoading() {
    _currentOverlay?.remove();
    _currentOverlay = null;
    _overlayContext = null;
  }

  /// Clear all loading states
  void clearAll() {
    _loadingStates.clear();
    _loadingMessages.clear();
    _loadingProgress.clear();
    hideGlobalLoading();
    notifyListeners();
  }

  @override
  void dispose() {
    hideGlobalLoading();
    super.dispose();
  }
}

/// Global loading overlay widget
class GlobalLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool dismissible;
  final Color? backgroundColor;
  final VoidCallback? onDismiss;

  const GlobalLoadingOverlay({
    super.key,
    this.message,
    this.dismissible = false,
    this.backgroundColor,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: backgroundColor ?? Colors.black.withValues(alpha: 0.5),
      child: GestureDetector(
        onTap: dismissible ? onDismiss : null,
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).primaryBackground,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 40,
                    height: 40,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        FlutterFlowTheme.of(context).primary,
                      ),
                      strokeWidth: 3.0,
                    ),
                  ),
                  if (message != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      message!,
                      style: FlutterFlowTheme.of(context).bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                  if (dismissible) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Tap to dismiss',
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            color: FlutterFlowTheme.of(context).secondaryText,
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Enhanced loading state mixin with more features
mixin EnhancedLoadingStateMixin<T extends StatefulWidget> on State<T> {
  final EnhancedLoadingService _loadingService = EnhancedLoadingService();
  final Map<String, bool> _localLoadingStates = {};

  /// Get loading service instance
  EnhancedLoadingService get loadingService => _loadingService;

  /// Check if local operation is loading
  bool isLocalLoading(String operation) =>
      _localLoadingStates[operation] ?? false;

  /// Set local loading state
  void setLocalLoading(String operation, bool loading) {
    if (mounted) {
      setState(() {
        if (loading) {
          _localLoadingStates[operation] = true;
        } else {
          _localLoadingStates.remove(operation);
        }
      });
    }
  }

  /// Execute operation with local loading state
  Future<T> executeWithLocalLoading<T>(
    String operation,
    Future<T> Function() task, {
    String? globalMessage,
    bool showGlobalLoading = false,
  }) async {
    try {
      setLocalLoading(operation, true);

      if (showGlobalLoading) {
        _loadingService.showGlobalLoading(context, message: globalMessage);
      }

      final result = await task();
      return result;
    } finally {
      setLocalLoading(operation, false);

      if (showGlobalLoading) {
        _loadingService.hideGlobalLoading();
      }
    }
  }

  /// Build loading overlay for specific widget
  Widget buildLoadingOverlay({
    required Widget child,
    required String operation,
    String? message,
    Color? overlayColor,
  }) {
    final isLoading = isLocalLoading(operation);

    return Stack(
      children: [
        child,
        if (isLoading)
          Positioned.fill(
            child: Container(
              color: overlayColor ?? Colors.black.withValues(alpha: 0.3),
              child: LoadingHelper.standardLoader(
                color: FlutterFlowTheme.of(context).primary,
                message: message,
              ),
            ),
          ),
      ],
    );
  }

  /// Build loading button
  Widget buildLoadingButton({
    required String operation,
    required VoidCallback onPressed,
    required String text,
    String? loadingText,
    ButtonStyle? style,
    Widget? icon,
  }) {
    final isLoading = isLocalLoading(operation);

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: style,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      FlutterFlowTheme.of(context).info,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(loadingText ?? 'Loading...'),
              ],
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  icon,
                  const SizedBox(width: 8),
                ],
                Text(text),
              ],
            ),
    );
  }

  @override
  void dispose() {
    _localLoadingStates.clear();
    super.dispose();
  }
}

/// Loading state provider widget
class LoadingStateProvider extends StatelessWidget {
  final Widget child;
  final EnhancedLoadingService? loadingService;

  const LoadingStateProvider({
    super.key,
    required this.child,
    this.loadingService,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: loadingService ?? EnhancedLoadingService(),
      builder: (context, _) => child,
    );
  }
}

/// Loading state consumer widget
class LoadingStateConsumer extends StatelessWidget {
  final Widget Function(
      BuildContext context, EnhancedLoadingService loadingService) builder;
  final EnhancedLoadingService? loadingService;

  const LoadingStateConsumer({
    super.key,
    required this.builder,
    this.loadingService,
  });

  @override
  Widget build(BuildContext context) {
    final service = loadingService ?? EnhancedLoadingService();

    return ListenableBuilder(
      listenable: service,
      builder: (context, _) => builder(context, service),
    );
  }
}

/// Loading operation constants
class LoadingOperations {
  static const String login = 'login';
  static const String logout = 'logout';
  static const String fetchProducts = 'fetch_products';
  static const String fetchOrders = 'fetch_orders';
  static const String createOrder = 'create_order';
  static const String addToCart = 'add_to_cart';
  static const String removeFromCart = 'remove_from_cart';
  static const String updateProfile = 'update_profile';
  static const String withdraw = 'withdraw';
  static const String refreshData = 'refresh_data';
  static const String uploadImage = 'upload_image';
  static const String syncData = 'sync_data';
}
