{"@@locale": "en", "appTitle": "<PERSON><PERSON><PERSON>", "@appTitle": {"description": "The title of the application"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "signup": "Sign Up", "@signup": {"description": "Sign up button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "home": "Home", "@home": {"description": "Home page title"}, "products": "Products", "@products": {"description": "Products page title"}, "cart": "<PERSON><PERSON>", "@cart": {"description": "Shopping cart title"}, "orders": "Orders", "@orders": {"description": "Orders page title"}, "account": "Account", "@account": {"description": "Account page title"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "notifications": "Notifications", "@notifications": {"description": "Notifications page title"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "addToCart": "Add to Cart", "@addToCart": {"description": "Add to cart button text"}, "buyNow": "Buy Now", "@buyNow": {"description": "Buy now button text"}, "price": "Price", "@price": {"description": "Price label"}, "mainPrice": "Main Price", "@mainPrice": {"description": "Main price label"}, "minPrice": "<PERSON>", "@minPrice": {"description": "Minimum price label"}, "maxPrice": "Max Price", "@maxPrice": {"description": "Maximum price label"}, "currency": "IQD", "@currency": {"description": "Iraqi Dinar currency symbol"}, "total": "Total", "@total": {"description": "Total amount label"}, "subtotal": "Subtotal", "@subtotal": {"description": "Subtotal amount label"}, "earnings": "Earnings", "@earnings": {"description": "Earnings label"}, "availableBalance": "Available Balance", "@availableBalance": {"description": "Available balance label"}, "incomingEarnings": "Incoming Earnings", "@incomingEarnings": {"description": "Incoming earnings label"}, "withdraw": "Withdraw", "@withdraw": {"description": "Withdraw button text"}, "orderStatus": "Order Status", "@orderStatus": {"description": "Order status label"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "processing": "Processing", "@processing": {"description": "Processing status"}, "shipped": "Shipped", "@shipped": {"description": "Shipped status"}, "delivered": "Delivered", "@delivered": {"description": "Delivered status"}, "cancelled": "Cancelled", "@cancelled": {"description": "Cancelled status"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark mode setting label"}, "language": "Language", "@language": {"description": "Language setting label"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "profile": "Profile", "@profile": {"description": "Profile page title"}, "editProfile": "Edit Profile", "@editProfile": {"description": "Edit profile button text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "loading": "Loading...", "@loading": {"description": "Loading message"}, "error": "Error", "@error": {"description": "Error message"}, "success": "Success", "@success": {"description": "Success message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "noItemsFound": "No items found", "@noItemsFound": {"description": "No items found message"}, "emptyCart": "Your cart is empty", "@emptyCart": {"description": "Empty cart message"}, "noOrders": "No orders found", "@noOrders": {"description": "No orders message"}, "noNotifications": "No notifications", "@noNotifications": {"description": "No notifications message"}, "categories": "Categories", "@categories": {"description": "Categories label"}, "flashSale": "Flash Sale", "@flashSale": {"description": "Flash sale label"}, "popularItems": "Popular Items", "@popularItems": {"description": "Popular items label"}, "allItems": "All Items", "@allItems": {"description": "All items label"}, "favorites": "Favorites", "@favorites": {"description": "Favorites page title"}, "addToFavorites": "Add to Favorites", "@addToFavorites": {"description": "Add to favorites button text"}, "removeFromFavorites": "Remove from Favorites", "@removeFromFavorites": {"description": "Remove from favorites button text"}, "checkout": "Checkout", "@checkout": {"description": "Checkout button text"}, "shippingAddress": "Shipping Address", "@shippingAddress": {"description": "Shipping address label"}, "paymentMethod": "Payment Method", "@paymentMethod": {"description": "Payment method label"}, "placeOrder": "Place Order", "@placeOrder": {"description": "Place order button text"}, "orderPlaced": "Order placed successfully", "@orderPlaced": {"description": "Order placed success message"}, "adminPanel": "Admin Panel", "@adminPanel": {"description": "Admin panel title"}, "manageProducts": "Manage Products", "@manageProducts": {"description": "Manage products label"}, "manageOrders": "Manage Orders", "@manageOrders": {"description": "Manage orders label"}, "manageUsers": "Manage Users", "@manageUsers": {"description": "Manage users label"}, "analytics": "Analytics", "@analytics": {"description": "Analytics label"}, "aiAssistant": "AI Assistant", "@aiAssistant": {"description": "AI Assistant label"}, "askAI": "Ask AI", "@askAI": {"description": "Ask AI button text"}, "aiResponse": "AI Response", "@aiResponse": {"description": "AI response label"}}