// Enhanced Theme Service
// This service provides comprehensive theme management with advanced features

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;
import '../flutter_flow/flutter_flow_theme.dart';

/// Enhanced theme service with advanced theme management
class EnhancedThemeService extends ChangeNotifier {
  static final EnhancedThemeService _instance =
      EnhancedThemeService._internal();
  factory EnhancedThemeService() => _instance;
  EnhancedThemeService._internal();

  // Theme state
  ThemeMode _themeMode = ThemeMode.system;
  bool _useSystemTheme = true;
  double _textScaleFactor = 1.0;
  bool _highContrast = false;
  bool _reducedMotion = false;
  String _fontFamily = 'default';

  // Theme customization
  Color? _customPrimaryColor;
  Color? _customAccentColor;
  bool _useCustomColors = false;

  // Accessibility
  bool _largeText = false;
  bool _boldText = false;

  // Animation preferences
  Duration _animationDuration = const Duration(milliseconds: 300);
  Curve _animationCurve = Curves.easeInOut;

  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get useSystemTheme => _useSystemTheme;
  double get textScaleFactor => _textScaleFactor;
  bool get highContrast => _highContrast;
  bool get reducedMotion => _reducedMotion;
  String get fontFamily => _fontFamily;
  Color? get customPrimaryColor => _customPrimaryColor;
  Color? get customAccentColor => _customAccentColor;
  bool get useCustomColors => _useCustomColors;
  bool get largeText => _largeText;
  bool get boldText => _boldText;
  Duration get animationDuration => _animationDuration;
  Curve get animationCurve => _animationCurve;

  /// Initialize theme service
  Future<void> initialize() async {
    await _loadThemePreferences();
    await FlutterFlowTheme.initialize();
    developer.log('✅ Enhanced Theme Service initialized');
  }

  /// Load theme preferences from storage
  Future<void> _loadThemePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode
      final themeModeString = prefs.getString('theme_mode');
      if (themeModeString != null) {
        _themeMode = ThemeMode.values.firstWhere(
          (mode) => mode.toString() == themeModeString,
          orElse: () => ThemeMode.system,
        );
      }

      // Load other preferences
      _useSystemTheme = prefs.getBool('use_system_theme') ?? true;
      _textScaleFactor = prefs.getDouble('text_scale_factor') ?? 1.0;
      _highContrast = prefs.getBool('high_contrast') ?? false;
      _reducedMotion = prefs.getBool('reduced_motion') ?? false;
      _fontFamily = prefs.getString('font_family') ?? 'default';
      _largeText = prefs.getBool('large_text') ?? false;
      _boldText = prefs.getBool('bold_text') ?? false;

      // Load custom colors
      _useCustomColors = prefs.getBool('use_custom_colors') ?? false;
      final primaryColorValue = prefs.getInt('custom_primary_color');
      final accentColorValue = prefs.getInt('custom_accent_color');

      if (primaryColorValue != null) {
        _customPrimaryColor = Color(primaryColorValue);
      }
      if (accentColorValue != null) {
        _customAccentColor = Color(accentColorValue);
      }

      // Load animation preferences
      final animationDurationMs = prefs.getInt('animation_duration_ms');
      if (animationDurationMs != null) {
        _animationDuration = Duration(milliseconds: animationDurationMs);
      }
    } catch (e) {
      developer.log('Error loading theme preferences: $e');
    }
  }

  /// Save theme preferences to storage
  Future<void> _saveThemePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('theme_mode', _themeMode.toString());
      await prefs.setBool('use_system_theme', _useSystemTheme);
      await prefs.setDouble('text_scale_factor', _textScaleFactor);
      await prefs.setBool('high_contrast', _highContrast);
      await prefs.setBool('reduced_motion', _reducedMotion);
      await prefs.setString('font_family', _fontFamily);
      await prefs.setBool('large_text', _largeText);
      await prefs.setBool('bold_text', _boldText);
      await prefs.setBool('use_custom_colors', _useCustomColors);
      await prefs.setInt(
          'animation_duration_ms', _animationDuration.inMilliseconds);

      if (_customPrimaryColor != null) {
        await prefs.setInt(
            'custom_primary_color', _customPrimaryColor!.toARGB32());
      }
      if (_customAccentColor != null) {
        await prefs.setInt(
            'custom_accent_color', _customAccentColor!.toARGB32());
      }
    } catch (e) {
      developer.log('Error saving theme preferences: $e');
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      _useSystemTheme = mode == ThemeMode.system;

      await FlutterFlowTheme.saveThemeMode(mode);
      await _saveThemePreferences();

      // Update system UI overlay style
      _updateSystemUIOverlayStyle();

      notifyListeners();
      developer.log('Theme mode changed to: $mode');
    }
  }

  /// Toggle between light and dark mode
  Future<void> toggleThemeMode() async {
    final newMode =
        _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    await setThemeMode(newMode);
  }

  /// Set text scale factor
  Future<void> setTextScaleFactor(double factor) async {
    if (_textScaleFactor != factor) {
      _textScaleFactor = factor.clamp(0.8, 2.0);
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set high contrast mode
  Future<void> setHighContrast(bool enabled) async {
    if (_highContrast != enabled) {
      _highContrast = enabled;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set reduced motion
  Future<void> setReducedMotion(bool enabled) async {
    if (_reducedMotion != enabled) {
      _reducedMotion = enabled;

      // Update animation duration based on reduced motion
      _animationDuration = enabled
          ? const Duration(milliseconds: 100)
          : const Duration(milliseconds: 300);

      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set font family
  Future<void> setFontFamily(String family) async {
    if (_fontFamily != family) {
      _fontFamily = family;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set large text mode
  Future<void> setLargeText(bool enabled) async {
    if (_largeText != enabled) {
      _largeText = enabled;

      // Automatically adjust text scale factor
      if (enabled && _textScaleFactor < 1.2) {
        _textScaleFactor = 1.2;
      } else if (!enabled && _textScaleFactor > 1.0) {
        _textScaleFactor = 1.0;
      }

      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set bold text mode
  Future<void> setBoldText(bool enabled) async {
    if (_boldText != enabled) {
      _boldText = enabled;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set custom primary color
  Future<void> setCustomPrimaryColor(Color? color) async {
    if (_customPrimaryColor != color) {
      _customPrimaryColor = color;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set custom accent color
  Future<void> setCustomAccentColor(Color? color) async {
    if (_customAccentColor != color) {
      _customAccentColor = color;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Enable/disable custom colors
  Future<void> setUseCustomColors(bool enabled) async {
    if (_useCustomColors != enabled) {
      _useCustomColors = enabled;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Set animation duration
  Future<void> setAnimationDuration(Duration duration) async {
    if (_animationDuration != duration) {
      _animationDuration = duration;
      await _saveThemePreferences();
      notifyListeners();
    }
  }

  /// Reset to default theme settings
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _useSystemTheme = true;
    _textScaleFactor = 1.0;
    _highContrast = false;
    _reducedMotion = false;
    _fontFamily = 'default';
    _largeText = false;
    _boldText = false;
    _useCustomColors = false;
    _customPrimaryColor = null;
    _customAccentColor = null;
    _animationDuration = const Duration(milliseconds: 300);
    _animationCurve = Curves.easeInOut;

    await FlutterFlowTheme.saveThemeMode(_themeMode);
    await _saveThemePreferences();
    _updateSystemUIOverlayStyle();

    notifyListeners();
    developer.log('Theme settings reset to defaults');
  }

  /// Update system UI overlay style based on current theme
  void _updateSystemUIOverlayStyle() {
    final isDark = _themeMode == ThemeMode.dark ||
        (_themeMode == ThemeMode.system &&
            WidgetsBinding.instance.platformDispatcher.platformBrightness ==
                Brightness.dark);

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor:
            isDark ? const Color(0xFF3B460D) : const Color(0xFFFCF0E3),
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  /// Get current theme data with customizations
  ThemeData getThemeData(BuildContext context, {required bool isDark}) {
    final baseTheme = isDark ? _getDarkTheme() : _getLightTheme();

    return baseTheme.copyWith(
      textTheme: _getCustomTextTheme(baseTheme.textTheme),
      colorScheme: _getCustomColorScheme(baseTheme.colorScheme, isDark),
    );
  }

  /// Get light theme
  ThemeData _getLightTheme() {
    return ThemeData(
      brightness: Brightness.light,
      useMaterial3: false,
      primaryColor: _useCustomColors && _customPrimaryColor != null
          ? _customPrimaryColor!
          : FlutterFlowTheme.appPrimaryColor,
      scaffoldBackgroundColor: FlutterFlowTheme.appAlternateColor,
      colorScheme: ColorScheme.light(
        primary: _useCustomColors && _customPrimaryColor != null
            ? _customPrimaryColor!
            : FlutterFlowTheme.appPrimaryColor,
        secondary: _useCustomColors && _customAccentColor != null
            ? _customAccentColor!
            : FlutterFlowTheme.appSecondaryColor,
        tertiary: FlutterFlowTheme.appTertiaryColor,
        surface: FlutterFlowTheme.appAlternateColor,
      ),
    );
  }

  /// Get dark theme
  ThemeData _getDarkTheme() {
    return ThemeData(
      brightness: Brightness.dark,
      useMaterial3: false,
      primaryColor: _useCustomColors && _customPrimaryColor != null
          ? _customPrimaryColor!
          : FlutterFlowTheme.appPrimaryColor,
      scaffoldBackgroundColor: FlutterFlowTheme.appTertiaryColor,
      colorScheme: ColorScheme.dark(
        primary: _useCustomColors && _customPrimaryColor != null
            ? _customPrimaryColor!
            : FlutterFlowTheme.appPrimaryColor,
        secondary: _useCustomColors && _customAccentColor != null
            ? _customAccentColor!
            : FlutterFlowTheme.appSecondaryColor,
        tertiary: FlutterFlowTheme.appTertiaryColor,
        surface: const Color(0xFF2C3409),
      ),
    );
  }

  /// Get custom text theme
  TextTheme _getCustomTextTheme(TextTheme baseTheme) {
    return baseTheme.copyWith(
      // Apply text scale factor and bold text settings
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 32) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.displayLarge?.fontWeight,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: (baseTheme.displayMedium?.fontSize ?? 28) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.displayMedium?.fontWeight,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 24) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.displaySmall?.fontWeight,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize: (baseTheme.headlineLarge?.fontSize ?? 22) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.headlineLarge?.fontWeight,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: (baseTheme.headlineMedium?.fontSize ?? 20) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.headlineMedium?.fontWeight,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize: (baseTheme.headlineSmall?.fontSize ?? 18) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.bold : baseTheme.headlineSmall?.fontWeight,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: (baseTheme.bodyLarge?.fontSize ?? 16) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.w600 : baseTheme.bodyLarge?.fontWeight,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: (baseTheme.bodyMedium?.fontSize ?? 14) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.w600 : baseTheme.bodyMedium?.fontWeight,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: (baseTheme.bodySmall?.fontSize ?? 12) * _textScaleFactor,
        fontWeight:
            _boldText ? FontWeight.w600 : baseTheme.bodySmall?.fontWeight,
      ),
    );
  }

  /// Get custom color scheme
  ColorScheme _getCustomColorScheme(ColorScheme baseScheme, bool isDark) {
    if (!_useCustomColors) return baseScheme;

    return baseScheme.copyWith(
      primary: _customPrimaryColor ?? baseScheme.primary,
      secondary: _customAccentColor ?? baseScheme.secondary,
      // Apply high contrast adjustments if enabled
      onPrimary: _highContrast
          ? (isDark ? Colors.black : Colors.white)
          : baseScheme.onPrimary,
      onSecondary: _highContrast
          ? (isDark ? Colors.black : Colors.white)
          : baseScheme.onSecondary,
    );
  }

  /// Get theme preferences summary
  Map<String, dynamic> getThemePreferences() {
    return {
      'theme_mode': _themeMode.toString(),
      'use_system_theme': _useSystemTheme,
      'text_scale_factor': _textScaleFactor,
      'high_contrast': _highContrast,
      'reduced_motion': _reducedMotion,
      'font_family': _fontFamily,
      'large_text': _largeText,
      'bold_text': _boldText,
      'use_custom_colors': _useCustomColors,
      'custom_primary_color': _customPrimaryColor?.toARGB32(),
      'custom_accent_color': _customAccentColor?.toARGB32(),
      'animation_duration_ms': _animationDuration.inMilliseconds,
    };
  }
}
