// Network Error Handler with Retry Logic
// This service provides comprehensive network error handling and retry mechanisms

import 'package:flutter/material.dart';

import 'dart:async';
import 'dart:io';
import 'dart:developer' as developer;
import '../utils/error_handler.dart';
import '../services/enhanced_error_service.dart';

/// Network error handler with intelligent retry logic
class NetworkErrorHandler {
  static final NetworkErrorHandler _instance = NetworkErrorHandler._internal();
  factory NetworkErrorHandler() => _instance;
  NetworkErrorHandler._internal();

  // Retry configuration
  static const int _defaultMaxRetries = 3;
  static const Duration _defaultBaseDelay = Duration(seconds: 1);
  static const Duration _defaultMaxDelay = Duration(seconds: 30);
  static const double _defaultBackoffMultiplier = 2.0;

  // Network status tracking
  bool _isOnline = true;
  final StreamController<bool> _connectivityController =
      StreamController<bool>.broadcast();
  Timer? _connectivityTimer;

  /// Stream of connectivity status
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Current connectivity status
  bool get isOnline => _isOnline;

  /// Initialize network error handler
  void initialize() {
    _startConnectivityMonitoring();
    developer.log('✅ Network Error Handler initialized');
  }

  /// Execute network operation with retry logic
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _defaultMaxRetries,
    Duration baseDelay = _defaultBaseDelay,
    Duration maxDelay = _defaultMaxDelay,
    double backoffMultiplier = _defaultBackoffMultiplier,
    bool Function(dynamic error)? shouldRetry,
    String? operationName,
    Map<String, dynamic>? context,
  }) async {
    int attempt = 0;
    Duration currentDelay = baseDelay;

    while (attempt <= maxRetries) {
      try {
        // Check connectivity before attempting
        if (!_isOnline) {
          throw NetworkError('No internet connection available');
        }

        final result = await operation();

        // Log successful operation after retries
        if (attempt > 0) {
          developer.log(
              '✅ Operation succeeded after $attempt retries: $operationName');
        }

        return result;
      } catch (error) {
        attempt++;

        // Log error details
        EnhancedErrorService().logError(
          error,
          source: 'NetworkErrorHandler',
          context: {
            'operation': operationName ?? 'unknown',
            'attempt': attempt,
            'max_retries': maxRetries,
            ...?context,
          },
        );

        // Check if we should retry
        final isRetryable =
            shouldRetry?.call(error) ?? _isRetryableError(error);

        if (attempt > maxRetries || !isRetryable) {
          // Transform error to user-friendly network error
          throw _transformNetworkError(error, operationName);
        }

        // Wait before retry with exponential backoff
        final delayDuration = Duration(
          milliseconds: (currentDelay.inMilliseconds *
                  (backoffMultiplier * (attempt - 1)))
              .round()
              .clamp(
                baseDelay.inMilliseconds,
                maxDelay.inMilliseconds,
              ),
        );

        developer.log(
            '⏳ Retrying operation in ${delayDuration.inSeconds}s (attempt $attempt/$maxRetries)');
        await Future.delayed(delayDuration);

        currentDelay = Duration(
          milliseconds:
              (currentDelay.inMilliseconds * backoffMultiplier).round(),
        );
      }
    }

    throw NetworkError('Operation failed after $maxRetries retries');
  }

  /// Execute network operation with user feedback
  Future<T?> executeWithUserFeedback<T>(
    BuildContext context,
    Future<T> Function() operation, {
    String? operationName,
    String? loadingMessage,
    String? successMessage,
    String? errorMessage,
    bool showRetryDialog = true,
    int maxRetries = _defaultMaxRetries,
    Map<String, dynamic>? operationContext,
  }) async {
    try {
      // Show loading if message provided
      if (loadingMessage != null) {
        _showLoadingSnackBar(context, loadingMessage);
      }

      final result = await executeWithRetry(
        operation,
        maxRetries: maxRetries,
        operationName: operationName,
        context: operationContext,
      );

      // Hide loading and show success
      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        if (successMessage != null) {
          ErrorHandler.showSuccessToUser(context, message: successMessage);
        }
      }

      return result;
    } catch (error) {
      // Hide loading
      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }

      // Handle error with user feedback
      if (showRetryDialog && _isRetryableError(error)) {
        if (context.mounted) {
          final shouldRetry =
              await _showRetryDialog(context, error, operationName);
          if (shouldRetry && context.mounted) {
            return executeWithUserFeedback(
              context,
              operation,
              operationName: operationName,
              loadingMessage: loadingMessage,
              successMessage: successMessage,
              errorMessage: errorMessage,
              showRetryDialog: false, // Prevent infinite retry dialogs
              maxRetries: 1, // Single retry from user action
            );
          }
        }
      } else {
        // Show error message
        if (context.mounted) {
          final message = errorMessage ?? _getNetworkErrorMessage(error);
          ErrorHandler.showErrorToUser(context, message: message);
        }
      }

      return null;
    }
  }

  /// Check if error is retryable
  bool _isRetryableError(dynamic error) {
    if (error is SocketException) {
      // Network connectivity issues are retryable
      return true;
    }

    if (error is TimeoutException) {
      // Timeout errors are retryable
      return true;
    }

    if (error is HttpException) {
      // Some HTTP errors are retryable (5xx server errors)
      final message = error.message.toLowerCase();
      return message.contains('500') ||
          message.contains('502') ||
          message.contains('503') ||
          message.contains('504');
    }

    final errorString = error.toString().toLowerCase();

    // Network-related errors
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('unreachable') ||
        errorString.contains('dns') ||
        errorString.contains('socket')) {
      return true;
    }

    // Firebase retryable errors
    if (errorString.contains('unavailable') ||
        errorString.contains('deadline-exceeded') ||
        errorString.contains('resource-exhausted')) {
      return true;
    }

    return false;
  }

  /// Transform error to user-friendly network error
  NetworkError _transformNetworkError(dynamic error, String? operationName) {
    if (error is NetworkError) {
      return error;
    }

    if (error is SocketException) {
      return NetworkError(
          'Unable to connect to the server. Please check your internet connection.');
    }

    if (error is TimeoutException) {
      return NetworkError('The request timed out. Please try again.');
    }

    if (error is HttpException) {
      return NetworkError('Server error occurred. Please try again later.');
    }

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return NetworkError(
          'Network connection failed. Please check your internet connection.');
    }

    if (errorString.contains('timeout')) {
      return NetworkError('Request timed out. Please try again.');
    }

    if (errorString.contains('dns') || errorString.contains('host')) {
      return NetworkError('Unable to reach the server. Please try again.');
    }

    return NetworkError(
        'Network error occurred${operationName != null ? ' during $operationName' : ''}. Please try again.');
  }

  /// Get user-friendly network error message
  String _getNetworkErrorMessage(dynamic error) {
    if (error is NetworkError) {
      return error.message;
    }

    return _transformNetworkError(error, null).message;
  }

  /// Show loading snack bar
  void _showLoadingSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 12),
            Text(message),
          ],
        ),
        duration: const Duration(minutes: 1), // Long duration for loading
        backgroundColor: Colors.blue.shade600,
      ),
    );
  }

  /// Show retry dialog
  Future<bool> _showRetryDialog(
      BuildContext context, dynamic error, String? operationName) async {
    final errorMessage = _getNetworkErrorMessage(error);

    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.wifi_off, color: Colors.orange),
                SizedBox(width: 8),
                Text('Connection Error'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(errorMessage),
                if (operationName != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Operation: $operationName',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
                const SizedBox(height: 16),
                const Text('Would you like to try again?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Retry'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// Start connectivity monitoring
  void _startConnectivityMonitoring() {
    _connectivityTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkConnectivity();
    });
  }

  /// Check connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      final isConnected = result.isNotEmpty && result[0].rawAddress.isNotEmpty;

      if (_isOnline != isConnected) {
        _isOnline = isConnected;
        _connectivityController.add(_isOnline);

        developer.log(_isOnline
            ? '✅ Internet connection restored'
            : '❌ Internet connection lost');
      }
    } catch (e) {
      if (_isOnline) {
        _isOnline = false;
        _connectivityController.add(_isOnline);
        developer.log('❌ Internet connection lost');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    _connectivityTimer?.cancel();
    _connectivityController.close();
  }
}

/// Network operation wrapper with built-in error handling
class NetworkOperation<T> {
  final Future<T> Function() operation;
  final String? name;
  final int maxRetries;
  final Duration baseDelay;
  final bool Function(dynamic error)? shouldRetry;
  final Map<String, dynamic>? context;

  NetworkOperation({
    required this.operation,
    this.name,
    this.maxRetries = 3,
    this.baseDelay = const Duration(seconds: 1),
    this.shouldRetry,
    this.context,
  });

  /// Execute the operation with error handling
  Future<T> execute() {
    return NetworkErrorHandler().executeWithRetry(
      operation,
      maxRetries: maxRetries,
      baseDelay: baseDelay,
      shouldRetry: shouldRetry,
      operationName: name,
      context: context,
    );
  }

  /// Execute with user feedback
  Future<T?> executeWithFeedback(
    BuildContext context, {
    String? loadingMessage,
    String? successMessage,
    String? errorMessage,
    bool showRetryDialog = true,
  }) {
    return NetworkErrorHandler().executeWithUserFeedback(
      context,
      operation,
      operationName: name,
      loadingMessage: loadingMessage,
      successMessage: successMessage,
      errorMessage: errorMessage,
      showRetryDialog: showRetryDialog,
      maxRetries: maxRetries,
      operationContext: null,
    );
  }
}
