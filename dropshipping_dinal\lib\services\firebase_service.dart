import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/order_model.dart';
import 'balance_service.dart';

/// Service to handle all Firebase operations for the dropshipping app
class FirebaseService extends ChangeNotifier {
  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collection references
  CollectionReference get _productsCollection =>
      _firestore.collection('products');
  CollectionReference get _ordersCollection => _firestore.collection('orders');
  CollectionReference get _usersCollection => _firestore.collection('users');
  CollectionReference get _earningsCollection =>
      _firestore.collection('earnings');

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // ==================== PRODUCTS API ====================

  /// Get all products
  Stream<QuerySnapshot> getProducts() {
    return _productsCollection.snapshots();
  }

  /// Get product by ID
  Future<DocumentSnapshot> getProductById(String productId) {
    return _productsCollection.doc(productId).get();
  }

  /// Get products by category
  Stream<QuerySnapshot> getProductsByCategory(String category) {
    return _productsCollection
        .where('categories', arrayContains: category)
        .snapshots();
  }

  /// Get featured products
  Stream<QuerySnapshot> getFeaturedProducts() {
    return _productsCollection.where('featured', isEqualTo: true).snapshots();
  }

  // ==================== ORDERS API ====================

  /// Create a new order with enhanced error handling and retry logic
  Future<String> createOrder({
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<OrderItem> items,
    required double totalAmount,
    required double totalEarnings,
  }) async {
    // Enhanced retry logic with exponential backoff
    int retryCount = 0;
    const maxRetries = 5;
    const baseDelay = Duration(seconds: 1);

    // Comprehensive validation first
    final validationResult = _validateOrderData(
      userName: userName,
      userPhone: userPhone,
      address: address,
      city: city,
      items: items,
      totalAmount: totalAmount,
      totalEarnings: totalEarnings,
    );

    if (!validationResult.isValid) {
      throw OrderValidationException(validationResult.errors);
    }

    while (retryCount < maxRetries) {
      try {
        debugPrint('🔄 Order creation attempt ${retryCount + 1}/$maxRetries');

        // Check network connectivity
        if (!await _checkNetworkConnectivity()) {
          throw NetworkException('No internet connection available');
        }

        // Ensure user is authenticated
        final userId = currentUserId;
        if (userId == null) {
          throw AuthenticationException('User not authenticated');
        }

        // Verify user session is still valid
        await _verifyUserSession();

        debugPrint('✅ Pre-flight checks passed');

        // Create order with timeout protection
        final orderId = await _createOrderWithTimeout(
          userId: userId,
          userName: userName,
          userPhone: userPhone,
          address: address,
          city: city,
          items: items,
          totalAmount: totalAmount,
          totalEarnings: totalEarnings,
        );

        debugPrint('✅ Order created successfully: $orderId');
        return orderId;
      } catch (e) {
        retryCount++;
        debugPrint('❌ Order creation attempt $retryCount failed: $e');

        // Determine if error is retryable
        if (!_isRetryableError(e) || retryCount >= maxRetries) {
          debugPrint('❌ Non-retryable error or max retries reached: $e');

          // Save failed order for later retry if possible
          if (retryCount >= maxRetries && _isNetworkError(e)) {
            await _saveFailedOrderForRetry(
              userName: userName,
              userPhone: userPhone,
              address: address,
              city: city,
              items: items,
              totalAmount: totalAmount,
              totalEarnings: totalEarnings,
            );
          }

          rethrow;
        }

        // Calculate exponential backoff delay
        final delay = Duration(
            milliseconds: baseDelay.inMilliseconds * (1 << (retryCount - 1)));
        debugPrint('⏳ Waiting ${delay.inSeconds}s before retry...');
        await Future.delayed(delay);
      }
    }

    throw OrderCreationException(
        'Order creation failed after $maxRetries attempts');
  }

  /// Create order with timeout protection
  Future<String> _createOrderWithTimeout({
    required String userId,
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<OrderItem> items,
    required double totalAmount,
    required double totalEarnings,
  }) async {
    const timeout = Duration(seconds: 30);

    return await Future.any([
      _performOrderCreation(
        userId: userId,
        userName: userName,
        userPhone: userPhone,
        address: address,
        city: city,
        items: items,
        totalAmount: totalAmount,
        totalEarnings: totalEarnings,
      ),
      Future.delayed(timeout,
          () => throw TimeoutException('Order creation timeout', timeout)),
    ]);
  }

  /// Perform the actual order creation with atomic operations
  Future<String> _performOrderCreation({
    required String userId,
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<OrderItem> items,
    required double totalAmount,
    required double totalEarnings,
  }) async {
    // Use Firestore batch for atomic operations
    final batch = _firestore.batch();

    try {
      // Convert order items to Firestore format
      final itemsData = items
          .map((item) => {
                'productId': item.productId,
                'productName': item.productName,
                'productImage': item.productImage,
                'price': item.price,
                'mainPrice': item.mainPrice,
                'quantity': item.quantity,
                'color': item.color,
                'earnings': item.earnings,
              })
          .toList();

      // Create order document
      final orderRef = _ordersCollection.doc();
      final orderData = {
        'id': orderRef.id,
        'userId': userId,
        'userName': userName,
        'userPhone': userPhone,
        'address': address,
        'city': city,
        'items': itemsData,
        'totalAmount': totalAmount,
        'totalEarnings': totalEarnings,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'status': 'pending',
        'earningsConfirmed': false,
        'version': 1, // For optimistic locking
        'metadata': {
          'source': 'flutter_app',
          'deviceInfo': await _getDeviceInfo(),
          'appVersion': await _getAppVersion(),
        },
      };

      // Add admin panel compatibility fields to orderData
      orderData.addAll({
        'customerName': userName, // Admin panel expects customerName
        'customerPhone': userPhone, // Admin panel expects customerPhone
        'customerEmail': '', // Will be filled if available
        'shippingAddress':
            '$address, $city', // Combined address for admin panel
        'notes': '', // Empty notes field
        'trackingNumber': '', // Empty tracking number
      });

      batch.set(orderRef, orderData);

      // Add earnings record to batch
      final earningsRef = _earningsCollection.doc();
      batch.set(earningsRef, {
        'userId': userId,
        'orderId': orderRef.id,
        'amount': totalEarnings,
        'status': 'pending', // pending, confirmed
        'orderStatus': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user balance in batch (preserve existing availableBalance)
      final userBalanceRef = _firestore.collection('userBalances').doc(userId);
      batch.set(
          userBalanceRef,
          {
            'userId': userId,
            // Don't set availableBalance to 0 - preserve existing value
            'incomingEarnings': FieldValue.increment(totalEarnings),
            'totalEarnings': FieldValue.increment(totalEarnings),
            'updatedAt': FieldValue.serverTimestamp(),
          },
          SetOptions(merge: true));

      // Commit batch
      await batch.commit();

      // Add earnings to local balance service
      await balanceService.addIncomingEarnings(totalEarnings);

      debugPrint('✅ Order created successfully: ${orderRef.id}');
      return orderRef.id;
    } catch (e) {
      debugPrint('❌ Order creation failed: $e');
      rethrow;
    }
  }

  /// Get orders for current user
  Stream<QuerySnapshot> getUserOrders() {
    final userId = currentUserId;
    if (userId == null) {
      return Stream.empty();
    }

    // Simplified query without orderBy to avoid index requirement
    // We'll sort in the client side for now
    return _ordersCollection.where('userId', isEqualTo: userId).snapshots();
  }

  /// Check for confirmed earnings
  Future<void> checkConfirmedEarnings() async {
    final userId = currentUserId;
    if (userId == null) return;

    try {
      // First, update earnings status based on order status changes
      await _updateEarningsFromOrderStatus();

      // Get earnings that have been confirmed but not processed locally
      final earningsSnapshot = await _earningsCollection
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'confirmed')
          .get();

      double totalConfirmed = 0;

      // Process each confirmed earning
      for (var doc in earningsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final amount = (data['amount'] as num).toDouble();

        // Add to total
        totalConfirmed += amount;

        // Mark as processed
        await _earningsCollection.doc(doc.id).update({
          'status': 'processed',
        });
      }

      // If we have confirmed earnings, move them to available balance
      if (totalConfirmed > 0) {
        await balanceService.confirmEarnings(totalConfirmed);
      }
    } catch (e) {
      debugPrint('Error checking confirmed earnings: $e');
    }
  }

  /// Update earnings status based on order status changes
  Future<void> _updateEarningsFromOrderStatus() async {
    final userId = currentUserId;
    if (userId == null) return;

    try {
      // Get all pending earnings for this user
      final earningsSnapshot = await _earningsCollection
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'pending')
          .get();

      for (var earningsDoc in earningsSnapshot.docs) {
        final earningsData = earningsDoc.data() as Map<String, dynamic>;
        final orderId = earningsData['orderId'] as String;

        // Check the corresponding order status
        final orderDoc = await _ordersCollection.doc(orderId).get();
        if (orderDoc.exists) {
          final orderData = orderDoc.data() as Map<String, dynamic>;
          final orderStatus = orderData['status'] as String;

          // If order is delivered, confirm the earnings
          if (orderStatus == 'delivered') {
            await _earningsCollection.doc(earningsDoc.id).update({
              'status': 'confirmed',
              'orderStatus': 'delivered',
              'confirmedAt': FieldValue.serverTimestamp(),
            });
            debugPrint('✅ Earnings confirmed for delivered order: $orderId');
          }
        }
      }
    } catch (e) {
      debugPrint('Error updating earnings from order status: $e');
    }
  }

  // ==================== USER PROFILE API ====================

  /// Get user profile
  Future<DocumentSnapshot?> getUserProfile() async {
    final userId = currentUserId;
    if (userId == null) return null;

    try {
      return await _usersCollection.doc(userId).get();
    } catch (e) {
      debugPrint('Error getting user profile: $e');
      return null;
    }
  }

  /// Update user profile
  Future<void> updateUserProfile({
    required String name,
    required String phone,
    String? address,
  }) async {
    final userId = currentUserId;
    if (userId == null) return;

    try {
      final userData = {
        'name': name,
        'phone': phone,
        if (address != null) 'address': address,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _usersCollection.doc(userId).set(userData, SetOptions(merge: true));
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      rethrow;
    }
  }

  // ==================== EARNINGS API ====================

  /// Request withdrawal
  Future<bool> requestWithdrawal({
    required double amount,
    required String paymentMethod,
    required String accountNumber,
  }) async {
    final userId = currentUserId;
    if (userId == null) return false;

    try {
      // Check if user has enough balance
      if (!await balanceService.withdraw(amount)) {
        return false;
      }

      // Create withdrawal request
      await _firestore.collection('withdrawals').add({
        'userId': userId,
        'amount': amount,
        'paymentMethod': paymentMethod,
        'accountNumber': accountNumber,
        'status': 'pending', // pending, processed, rejected
        'createdAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error requesting withdrawal: $e');
      return false;
    }
  }

  /// Sync balance with Firebase
  Future<void> syncBalance() async {
    await checkConfirmedEarnings();
  }

  // ==================== HELPER METHODS ====================

  /// Validate order data
  ValidationResult _validateOrderData({
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<OrderItem> items,
    required double totalAmount,
    required double totalEarnings,
  }) {
    final errors = <String>[];

    if (userName.trim().isEmpty) errors.add('User name is required');
    if (userPhone.trim().isEmpty) errors.add('User phone is required');
    if (address.trim().isEmpty) errors.add('Address is required');
    if (city.trim().isEmpty) errors.add('City is required');
    if (items.isEmpty) errors.add('Order items are required');
    if (totalAmount <= 0) errors.add('Total amount must be greater than 0');
    if (totalEarnings < 0) errors.add('Total earnings cannot be negative');

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// Check network connectivity
  Future<bool> _checkNetworkConnectivity() async {
    try {
      // Simple connectivity check by trying to reach Firebase
      await _firestore.collection('_connectivity_test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Verify user session
  Future<void> _verifyUserSession() async {
    final user = _auth.currentUser;
    if (user == null) {
      throw AuthenticationException('User session expired');
    }

    try {
      await user.reload();
    } catch (e) {
      throw AuthenticationException('Failed to verify user session: $e');
    }
  }

  /// Check if error is retryable
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('unavailable') ||
        errorString.contains('deadline') ||
        errorString.contains('cancelled');
  }

  /// Check if error is network related
  bool _isNetworkError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
        errorString.contains('internet') ||
        errorString.contains('connection');
  }

  /// Save failed order for retry
  Future<void> _saveFailedOrderForRetry({
    required String userName,
    required String userPhone,
    required String address,
    required String city,
    required List<OrderItem> items,
    required double totalAmount,
    required double totalEarnings,
  }) async {
    try {
      await _firestore.collection('failed_orders').add({
        'userId': currentUserId,
        'userName': userName,
        'userPhone': userPhone,
        'address': address,
        'city': city,
        'items': items.map((item) => item.toMap()).toList(),
        'totalAmount': totalAmount,
        'totalEarnings': totalEarnings,
        'failedAt': FieldValue.serverTimestamp(),
        'retryCount': 0,
        'status': 'pending_retry',
      });
    } catch (e) {
      debugPrint('Failed to save failed order: $e');
    }
  }

  /// Get device info
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': 'flutter',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Get app version
  Future<String> _getAppVersion() async {
    return '1.0.0'; // You can use package_info_plus to get actual version
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final List<String> errors;

  ValidationResult({
    required this.isValid,
    required this.errors,
  });
}

/// Custom exceptions
class OrderValidationException implements Exception {
  final List<String> errors;
  OrderValidationException(this.errors);

  @override
  String toString() => 'OrderValidationException: ${errors.join(', ')}';
}

class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}

class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

class OrderCreationException implements Exception {
  final String message;
  OrderCreationException(this.message);

  @override
  String toString() => 'OrderCreationException: $message';
}

class TimeoutException implements Exception {
  final String message;
  final Duration timeout;
  TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (${timeout.inSeconds}s)';
}

// Global instance for easy access
final firebaseService = FirebaseService();
