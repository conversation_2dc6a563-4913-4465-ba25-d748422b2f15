// IMMEDIATE FIX: Transfer earnings for delivered orders
const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  const serviceAccount = require('./dropshippingdinal-vq5iag-firebase-adminsdk-fbsvc-c5f412b27c.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://dropshippingdinal-vq5iag-default-rtdb.firebaseio.com'
  });
}

const db = admin.firestore();
const FieldValue = admin.firestore.FieldValue;

// Your user ID
const USER_ID = 'H4yisnMzvENCdpRT6rSHmdn7R4t1';

async function fixEarningsNow() {
  console.log('🚀 IMMEDIATE EARNINGS FIX');
  console.log('=========================\n');

  try {
    console.log(`👤 Fixing earnings for user: ${USER_ID}\n`);

    // Get all delivered orders that need earnings confirmation
    const ordersSnapshot = await db.collection('orders')
      .where('userId', '==', USER_ID)
      .where('status', 'in', ['Delivered', 'delivered'])
      .get();

    console.log(`📦 Found ${ordersSnapshot.size} delivered orders\n`);

    let fixedCount = 0;
    let totalEarningsTransferred = 0;

    for (const orderDoc of ordersSnapshot.docs) {
      const orderData = orderDoc.data();
      const orderId = orderDoc.id;
      const totalEarnings = orderData.totalEarnings || 0.0;
      const earningsConfirmed = orderData.earningsConfirmed || false;

      console.log(`🔄 Processing order: ${orderId.substring(0, 8)}...`);
      console.log(`   Total Earnings: $${totalEarnings.toFixed(2)}`);
      console.log(`   Already Confirmed: ${earningsConfirmed}`);

      if (earningsConfirmed) {
        console.log('   ✅ Already processed, skipping\n');
        continue;
      }

      if (totalEarnings <= 0) {
        console.log('   ⚠️  No earnings to process, skipping\n');
        continue;
      }

      try {
        // Use Firebase transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
          // 1. Update the order to mark earnings as confirmed
          const orderRef = db.collection('orders').doc(orderId);
          transaction.update(orderRef, {
            earningsConfirmed: true,
            earningsConfirmedAt: FieldValue.serverTimestamp(),
            updatedAt: FieldValue.serverTimestamp(),
          });

          // 2. Update userBalances collection (Flutter app format)
          const userBalanceRef = db.collection('userBalances').doc(USER_ID);
          transaction.set(userBalanceRef, {
            userId: USER_ID,
            availableBalance: FieldValue.increment(totalEarnings),
            incomingEarnings: FieldValue.increment(-totalEarnings),
            totalEarnings: FieldValue.increment(0), // No change to total
            updatedAt: FieldValue.serverTimestamp(),
          }, { merge: true });

          // 3. Update earnings collection
          const earningsQuery = await db.collection('earnings')
            .where('orderId', '==', orderId)
            .where('userId', '==', USER_ID)
            .get();

          if (!earningsQuery.empty) {
            const earningsRef = earningsQuery.docs[0].ref;
            transaction.update(earningsRef, {
              status: 'confirmed',
              orderStatus: 'delivered',
              confirmedAt: FieldValue.serverTimestamp(),
              updatedAt: FieldValue.serverTimestamp(),
            });
          } else {
            // Create earnings record if it doesn't exist
            const newEarningsRef = db.collection('earnings').doc();
            transaction.set(newEarningsRef, {
              userId: USER_ID,
              orderId: orderId,
              amount: totalEarnings,
              status: 'confirmed',
              orderStatus: 'delivered',
              createdAt: FieldValue.serverTimestamp(),
              confirmedAt: FieldValue.serverTimestamp(),
              updatedAt: FieldValue.serverTimestamp(),
            });
          }

          // 4. Also update users collection for admin panel compatibility
          const userRef = db.collection('users').doc(USER_ID);
          transaction.set(userRef, {
            currentBalance: FieldValue.increment(totalEarnings),
            totalEarnings: FieldValue.increment(totalEarnings),
            lastActivity: FieldValue.serverTimestamp(),
          }, { merge: true });

          // 5. Create transaction record
          const transactionRef = db.collection('transactions').doc();
          transaction.set(transactionRef, {
            id: transactionRef.id,
            userId: USER_ID,
            type: 'earning',
            amount: totalEarnings,
            description: `Commission from delivered order #${orderId.substring(0, 8)}`,
            orderId: orderId,
            createdAt: FieldValue.serverTimestamp(),
            status: 'completed'
          });
        });

        console.log(`   ✅ Successfully transferred $${totalEarnings.toFixed(2)} to available balance`);
        fixedCount++;
        totalEarningsTransferred += totalEarnings;

      } catch (error) {
        console.log(`   ❌ Error processing order: ${error.message}`);
      }

      console.log('');
    }

    // Verify the fix
    console.log('🔍 Verifying the fix...\n');
    const balanceDoc = await db.collection('userBalances').doc(USER_ID).get();
    const balanceData = balanceDoc.data();

    console.log('📊 Updated Balance Status:');
    console.log(`   Available Balance: $${balanceData?.availableBalance?.toFixed(2) || '0.00'}`);
    console.log(`   Incoming Earnings: $${balanceData?.incomingEarnings?.toFixed(2) || '0.00'}`);
    console.log(`   Total Earnings: $${balanceData?.totalEarnings?.toFixed(2) || '0.00'}`);

    console.log('\n📈 Fix Summary:');
    console.log(`   ✅ Orders Fixed: ${fixedCount}`);
    console.log(`   💰 Total Earnings Transferred: $${totalEarningsTransferred.toFixed(2)}`);

    if (fixedCount > 0) {
      console.log('\n🎉 SUCCESS! Earnings have been transferred to available balance!');
      console.log('💡 Check your Flutter app withdrawal page - the balance should now be updated');
    } else {
      console.log('\n⚠️  No orders needed fixing');
    }

  } catch (error) {
    console.error('❌ Error during earnings fix:', error);
  }
}

// Run the fix
fixEarningsNow().then(() => {
  console.log('\n🎯 Earnings fix completed!');
  process.exit(0);
}).catch(console.error);
