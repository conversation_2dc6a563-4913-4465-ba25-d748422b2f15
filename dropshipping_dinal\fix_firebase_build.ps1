Write-Host "🔧 Firebase Build Fix Script" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

Write-Host "📦 Building Flutter web app..." -ForegroundColor Yellow
flutter build web

$buildIndexPath = "build\web\index.html"

if (-not (Test-Path $buildIndexPath)) {
    Write-Host "❌ Error: Built index.html not found at $buildIndexPath" -ForegroundColor Red
    exit 1
}

Write-Host "🔥 Adding Firebase scripts to built index.html..." -ForegroundColor Yellow

# Read the current content
$content = Get-Content $buildIndexPath -Raw

# Define the Firebase scripts to inject
$firebaseScripts = @"

  <!-- Firebase SDK Scripts - CRITICAL for Firebase to work on web -->
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/11.7.0/firebase-storage.js"></script>

  <!-- Firebase Configuration Script -->
  <script>
    // Firebase configuration for dropshipping dinal
    const firebaseConfig = {
      apiKey: "AIzaSyC-z8lo2VETMz93qEnONSaHth3e9d2Ux9I",
      authDomain: "dropshippingdinal-vq5iag.firebaseapp.com",
      projectId: "dropshippingdinal-vq5iag",
      storageBucket: "dropshippingdinal-vq5iag.firebasestorage.app",
      messagingSenderId: "686495930941",
      appId: "1:686495930941:web:76ed7f7128907c4f856854"
    };

    // Initialize Firebase BEFORE Flutter app starts
    try {
      console.log('🔥 Initializing Firebase from HTML...');
      firebase.initializeApp(firebaseConfig);
      console.log('✅ Firebase initialized successfully from HTML');

      // Mark Firebase as ready for Flutter
      window.firebaseReady = true;

      // Verify Firebase services are accessible
      if (firebase.auth && firebase.firestore) {
        console.log('✅ Firebase Auth and Firestore services available');
      } else {
        console.warn('⚠️ Some Firebase services may not be available');
      }
    } catch (error) {
      console.error('❌ Firebase initialization failed in HTML:', error);
      window.firebaseReady = false;
      window.firebaseError = error.message;
    }
  </script>

"@

# Replace the manifest line with manifest + Firebase scripts
$newContent = $content -replace '  <link rel="manifest" href="manifest.json">', "  <link rel=`"manifest`" href=`"manifest.json`">$firebaseScripts"

# Write the updated content back
Set-Content -Path $buildIndexPath -Value $newContent -Encoding UTF8

Write-Host "✅ Firebase scripts successfully added to $buildIndexPath" -ForegroundColor Green
Write-Host "🚀 Build complete! Your app is ready with Firebase support." -ForegroundColor Green
Write-Host ""
Write-Host "📍 To serve the app locally, run:" -ForegroundColor Cyan
Write-Host "   flutter run -d web-server --web-port=8080" -ForegroundColor White
Write-Host ""
