import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

/// Debug script to check the specific order that's showing as delivered
/// but earnings aren't moving to available balance

Future<void> main() async {
  print('🔍 DEBUGGING SPECIFIC ORDER EARNINGS FLOW');
  print('==========================================\n');

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    final firestore = FirebaseFirestore.instance;
    final auth = FirebaseAuth.instance;

    // Get current user
    final user = auth.currentUser;
    if (user == null) {
      print('❌ No user signed in. Please sign in first.');
      return;
    }

    final userId = user.uid;
    print('👤 Current user: $userId\n');

    // Step 1: Find the delivered order
    await findDeliveredOrder(firestore, userId);

    // Step 2: Check user balance
    await checkUserBalance(firestore, userId);

    // Step 3: Check earnings records
    await checkEarningsRecords(firestore, userId);

    // Step 4: Check OrderBalanceSyncService status
    await checkSyncServiceStatus();

    // Step 5: Test manual sync
    await testManualSync(firestore, userId);

  } catch (e) {
    print('❌ Error during debugging: $e');
  }
}

/// Find the delivered order that should trigger earnings transfer
Future<void> findDeliveredOrder(FirebaseFirestore firestore, String userId) async {
  print('📦 STEP 1: Finding delivered orders...\n');

  try {
    // Get all orders for this user
    final ordersSnapshot = await firestore
        .collection('orders')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .get();

    print('📊 Total orders found: ${ordersSnapshot.docs.length}');

    for (final orderDoc in ordersSnapshot.docs) {
      final orderData = orderDoc.data();
      final orderId = orderDoc.id;
      final status = orderData['status'] ?? 'unknown';
      final totalAmount = orderData['totalAmount'] ?? 0.0;
      final totalEarnings = orderData['totalEarnings'] ?? 0.0;
      final earningsConfirmed = orderData['earningsConfirmed'] ?? false;
      final createdAt = orderData['createdAt'];

      print('📋 Order: ${orderId.substring(0, 8)}...');
      print('   Status: $status');
      print('   Total Amount: \$${totalAmount.toStringAsFixed(2)}');
      print('   Total Earnings: \$${totalEarnings.toStringAsFixed(2)}');
      print('   Earnings Confirmed: $earningsConfirmed');
      print('   Created: ${createdAt?.toDate() ?? 'Unknown'}');

      // Check if this is a delivered order that should have triggered earnings transfer
      if (status.toLowerCase() == 'delivered' || status == 'Delivered') {
        print('   🚚 THIS IS A DELIVERED ORDER!');
        
        if (!earningsConfirmed && totalEarnings > 0) {
          print('   ⚠️  ISSUE: Earnings not confirmed despite delivery!');
          print('   💡 This order should trigger earnings transfer');
          
          // Check if there's an earnings record for this order
          await checkOrderEarningsRecord(firestore, userId, orderId);
        } else if (earningsConfirmed) {
          print('   ✅ Earnings already confirmed for this order');
        } else {
          print('   ⚠️  No earnings to process for this order');
        }
      }
      print('');
    }
  } catch (e) {
    print('❌ Error finding delivered orders: $e');
  }
}

/// Check earnings record for a specific order
Future<void> checkOrderEarningsRecord(FirebaseFirestore firestore, String userId, String orderId) async {
  try {
    final earningsSnapshot = await firestore
        .collection('earnings')
        .where('orderId', isEqualTo: orderId)
        .where('userId', isEqualTo: userId)
        .get();

    if (earningsSnapshot.docs.isEmpty) {
      print('   ❌ NO EARNINGS RECORD FOUND for this order!');
      print('   💡 This might be why earnings aren\'t transferring');
    } else {
      final earningsData = earningsSnapshot.docs.first.data();
      print('   📈 Earnings record found:');
      print('      Amount: \$${earningsData['amount']?.toStringAsFixed(2) ?? '0.00'}');
      print('      Status: ${earningsData['status'] ?? 'unknown'}');
      print('      Order Status: ${earningsData['orderStatus'] ?? 'unknown'}');
    }
  } catch (e) {
    print('   ❌ Error checking earnings record: $e');
  }
}

/// Check user balance in userBalances collection
Future<void> checkUserBalance(FirebaseFirestore firestore, String userId) async {
  print('💰 STEP 2: Checking user balance...\n');

  try {
    final balanceDoc = await firestore.collection('userBalances').doc(userId).get();

    if (!balanceDoc.exists) {
      print('❌ NO USER BALANCE DOCUMENT FOUND!');
      print('💡 This is a critical issue - userBalances document missing');
      return;
    }

    final balanceData = balanceDoc.data()!;
    print('📊 User Balance Status:');
    print('   Available Balance: \$${balanceData['availableBalance']?.toStringAsFixed(2) ?? '0.00'}');
    print('   Incoming Earnings: \$${balanceData['incomingEarnings']?.toStringAsFixed(2) ?? '0.00'}');
    print('   Total Earnings: \$${balanceData['totalEarnings']?.toStringAsFixed(2) ?? '0.00'}');
    print('   Last Updated: ${balanceData['updatedAt']?.toDate() ?? 'Unknown'}');
    print('');

    // Check if there are incoming earnings that should be transferred
    final incomingEarnings = balanceData['incomingEarnings'] ?? 0.0;
    if (incomingEarnings > 0) {
      print('⚠️  ISSUE: You have \$${incomingEarnings.toStringAsFixed(2)} in incoming earnings');
      print('💡 These should move to available balance when orders are delivered');
    }
  } catch (e) {
    print('❌ Error checking user balance: $e');
  }
}

/// Check earnings records for the user
Future<void> checkEarningsRecords(FirebaseFirestore firestore, String userId) async {
  print('📈 STEP 3: Checking earnings records...\n');

  try {
    final earningsSnapshot = await firestore
        .collection('earnings')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .get();

    print('📊 Total earnings records: ${earningsSnapshot.docs.length}');

    for (final earningsDoc in earningsSnapshot.docs) {
      final earningsData = earningsDoc.data();
      final orderId = earningsData['orderId'] ?? 'unknown';
      final amount = earningsData['amount'] ?? 0.0;
      final status = earningsData['status'] ?? 'unknown';
      final orderStatus = earningsData['orderStatus'] ?? 'unknown';

      print('📋 Earnings Record:');
      print('   Order: ${orderId.substring(0, 8)}...');
      print('   Amount: \$${amount.toStringAsFixed(2)}');
      print('   Status: $status');
      print('   Order Status: $orderStatus');

      if (orderStatus.toLowerCase() == 'delivered' && status != 'confirmed') {
        print('   ⚠️  ISSUE: Order delivered but earnings not confirmed!');
      }
      print('');
    }
  } catch (e) {
    print('❌ Error checking earnings records: $e');
  }
}

/// Check if OrderBalanceSyncService is working
Future<void> checkSyncServiceStatus() async {
  print('🔄 STEP 4: Checking OrderBalanceSyncService status...\n');

  try {
    // Import the service to check its status
    // Note: This would need to be adapted based on your app structure
    print('💡 OrderBalanceSyncService status check:');
    print('   - Service should be initialized in main.dart');
    print('   - Service should listen for order status changes');
    print('   - Service should automatically transfer earnings when orders are delivered');
    print('   - Check console logs for service activity');
    print('');
  } catch (e) {
    print('❌ Error checking sync service: $e');
  }
}

/// Test manual sync functionality
Future<void> testManualSync(FirebaseFirestore firestore, String userId) async {
  print('🔧 STEP 5: Testing manual sync...\n');

  try {
    // Get all delivered orders that haven't had earnings confirmed
    final ordersSnapshot = await firestore
        .collection('orders')
        .where('userId', isEqualTo: userId)
        .where('status', isEqualTo: 'Delivered')
        .where('earningsConfirmed', isEqualTo: false)
        .get();

    print('📦 Delivered orders needing earnings confirmation: ${ordersSnapshot.docs.length}');

    if (ordersSnapshot.docs.isNotEmpty) {
      print('💡 MANUAL FIX NEEDED:');
      print('   These orders are delivered but earnings haven\'t been transferred:');
      
      for (final orderDoc in ordersSnapshot.docs) {
        final orderData = orderDoc.data();
        final orderId = orderDoc.id;
        final totalEarnings = orderData['totalEarnings'] ?? 0.0;
        
        print('   - Order ${orderId.substring(0, 8)}...: \$${totalEarnings.toStringAsFixed(2)}');
      }
      
      print('\n🔧 RECOMMENDED ACTIONS:');
      print('1. Run the migration script to fix existing data');
      print('2. Check admin panel integration');
      print('3. Verify OrderBalanceSyncService is working');
      print('4. Test the unified earnings API endpoint');
    } else {
      print('✅ No delivered orders found that need earnings confirmation');
    }
  } catch (e) {
    print('❌ Error during manual sync test: $e');
  }
}
