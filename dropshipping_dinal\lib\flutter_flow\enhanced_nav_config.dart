// Enhanced Navigation Configuration with Deep Linking Support
// This file provides enhanced route configuration and deep linking capabilities

import 'package:flutter/material.dart';
import '../index.dart';
import '../services/enhanced_navigation_service.dart';
import 'nav/nav.dart';

/// Enhanced route configuration with deep linking support
class EnhancedNavConfig {
  /// Create enhanced router with deep linking support
  static GoRouter createEnhancedRouter(AppStateNotifier appStateNotifier) {
    return GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      navigatorKey: appNavigatorKey,
      errorBuilder: (context, state) =>
          _buildErrorPage(context, state, appStateNotifier),
      redirect: (context, state) =>
          _handleGlobalRedirect(context, state, appStateNotifier),
      routes: [
        // Root route with initialization logic
        GoRoute(
          name: '_initialize',
          path: '/',
          builder: (context, state) =>
              _buildInitializationPage(context, appStateNotifier),
        ),

        // Authentication routes
        GoRoute(
          name: 'login',
          path: '/login',
          builder: (context, state) => const LoginWidget(),
        ),

        // Main app routes
        GoRoute(
          name: 'home',
          path: '/home',
          builder: (context, state) => const HomePageWidget(),
        ),

        // Product routes with deep linking
        GoRoute(
          name: 'products',
          path: '/products',
          builder: (context, state) => const AllitemsWidget(),
          routes: [
            GoRoute(
              name: 'product-detail',
              path: '/:productId',
              builder: (context, state) {
                final productId = state.pathParameters['productId'];
                return FirebaseProductPageWidget(productId: productId);
              },
            ),
          ],
        ),

        // Shopping cart routes
        GoRoute(
          name: 'cart',
          path: '/cart',
          builder: (context, state) => const ShopingcartWidget(),
          routes: [
            GoRoute(
              name: 'checkout',
              path: '/checkout',
              builder: (context, state) => const CustomerinfoWidget(),
            ),
          ],
        ),

        // Order routes
        GoRoute(
          name: 'orders',
          path: '/orders',
          builder: (context, state) => const OrdersWidget(),
          routes: [
            GoRoute(
              name: 'order-detail',
              path: '/:orderId',
              builder: (context, state) {
                final orderId = state.pathParameters['orderId'];
                return OrderDetailWidget(orderId: orderId);
              },
            ),
          ],
        ),

        // Account and settings routes
        GoRoute(
          name: 'account',
          path: '/account',
          builder: (context, state) => const AccountWidget(),
        ),

        GoRoute(
          name: 'settings',
          path: '/settings',
          builder: (context, state) => const SettingsWidget(),
        ),

        // Financial routes
        GoRoute(
          name: 'withdraw',
          path: '/withdraw',
          builder: (context, state) => const WithdrawWidget(),
        ),

        // Category routes
        GoRoute(
          name: 'popular',
          path: '/popular',
          builder: (context, state) => const PopularitemWidget(),
        ),

        GoRoute(
          name: 'flash-sale',
          path: '/flash-sale',
          builder: (context, state) => const FlashsaleWidget(),
        ),

        // Admin routes (protected)
        GoRoute(
          name: 'admin',
          path: '/admin',
          builder: (context, state) => const AdminpanelWidget(),
          redirect: (context, state) {
            // Add admin authentication check here
            return null; // Allow access for now
          },
        ),

        // Deep linking routes
        GoRoute(
          name: 'deep-link',
          path: '/link/:type/:id',
          builder: (context, state) {
            final type = state.pathParameters['type'];
            final id = state.pathParameters['id'];
            return _handleDeepLink(context, type, id);
          },
        ),

        // Legacy route support
        ...legacyRoutes,
      ],
    );
  }

  /// Build initialization page with loading logic
  static Widget _buildInitializationPage(
      BuildContext context, AppStateNotifier appStateNotifier) {
    return FutureBuilder<bool>(
      future: appStateNotifier.isFirstLaunch(),
      builder: (context, snapshot) {
        // Show loading while checking first launch
        if (snapshot.connectionState == ConnectionState.waiting) {
          return _buildLoadingPage(context);
        }

        final isFirstLaunch = snapshot.data ?? false;

        // If first launch, always show login
        if (isFirstLaunch) {
          return const LoginWidget();
        }

        // Otherwise, check login status
        return appStateNotifier.loggedIn
            ? const HomePageWidget()
            : const LoginWidget();
      },
    );
  }

  /// Build error page for navigation errors
  static Widget _buildErrorPage(BuildContext context, GoRouterState state,
      AppStateNotifier appStateNotifier) {
    return Scaffold(
      appBar: EnhancedNavigationWidgets.standardAppBar(
        context,
        title: 'Page Not Found',
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you requested could not be found.',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go to Home'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build loading page
  static Widget _buildLoadingPage(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      ),
    );
  }

  /// Handle global redirects
  static String? _handleGlobalRedirect(BuildContext context,
      GoRouterState state, AppStateNotifier appStateNotifier) {
    // Handle app state redirects
    if (appStateNotifier.shouldRedirect) {
      final redirectLocation = appStateNotifier.getRedirectLocation();
      appStateNotifier.clearRedirectLocation();
      return redirectLocation;
    }

    // Handle authentication redirects
    final protectedRoutes = [
      '/orders',
      '/account',
      '/withdraw',
      '/admin',
    ];

    if (protectedRoutes.any((route) => state.uri.path.startsWith(route))) {
      if (!appStateNotifier.loggedIn) {
        appStateNotifier.setRedirectLocationIfUnset(state.uri.toString());
        return '/login';
      }
    }

    return null;
  }

  /// Handle deep link navigation
  static Widget _handleDeepLink(
      BuildContext context, String? type, String? id) {
    if (type == null || id == null) {
      return _buildErrorPage(
          context, GoRouterState.of(context), AppStateNotifier.instance);
    }

    switch (type) {
      case 'product':
        return FirebaseProductPageWidget(productId: id);
      case 'order':
        return OrderDetailWidget(orderId: id);
      case 'category':
        return const AllitemsWidget(); // Category navigation goes to all items
      default:
        return _buildErrorPage(
            context, GoRouterState.of(context), AppStateNotifier.instance);
    }
  }

  /// Legacy routes for backward compatibility
  static List<GoRoute> get legacyRoutes => [
        // Legacy route mappings
        GoRoute(
          name: 'HomePageWidget',
          path: '/HomePageWidget',
          redirect: (context, state) => '/',
        ),
        GoRoute(
          name: 'AllitemsWidget',
          path: '/AllitemsWidget',
          redirect: (context, state) => '/products',
        ),
        GoRoute(
          name: 'OrdersWidget',
          path: '/OrdersWidget',
          redirect: (context, state) => '/orders',
        ),
        GoRoute(
          name: 'ShopingcartWidget',
          path: '/ShopingcartWidget',
          redirect: (context, state) => '/cart',
        ),
        GoRoute(
          name: 'WithdrawWidget',
          path: '/WithdrawWidget',
          redirect: (context, state) => '/withdraw',
        ),
        GoRoute(
          name: 'AccountWidget',
          path: '/AccountWidget',
          redirect: (context, state) => '/account',
        ),
        GoRoute(
          name: 'SettingsWidget',
          path: '/SettingsWidget',
          redirect: (context, state) => '/settings',
        ),
        GoRoute(
          name: 'CustomerinfoWidget',
          path: '/CustomerinfoWidget',
          redirect: (context, state) => '/cart/checkout',
        ),
      ];
}

/// Deep linking helper
class DeepLinkHelper {
  /// Parse and handle deep link
  static void handleDeepLink(BuildContext context, String link) {
    try {
      final uri = Uri.parse(link);
      final path = uri.path;
      final queryParams = uri.queryParameters;

      // Handle different deep link patterns
      if (path.startsWith('/product/')) {
        final productId = path.split('/').last;
        context.go('/products/$productId', extra: queryParams);
      } else if (path.startsWith('/order/')) {
        final orderId = path.split('/').last;
        context.go('/orders/$orderId', extra: queryParams);
      } else if (path.startsWith('/cart')) {
        context.go('/cart', extra: queryParams);
      } else if (path.startsWith('/category/')) {
        final category = path.split('/').last;
        context.go('/products?category=$category', extra: queryParams);
      } else {
        // Default to home for unknown links
        context.go('/');
      }
    } catch (e) {
      debugPrint('Deep link parsing error: $e');
      context.go('/');
    }
  }

  /// Generate deep link for sharing
  static String generateDeepLink({
    required String type,
    required String id,
    Map<String, String>? queryParams,
  }) {
    final baseUrl = 'https://yourapp.com'; // Replace with your app's URL
    var link = '$baseUrl/link/$type/$id';

    if (queryParams != null && queryParams.isNotEmpty) {
      final query = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      link += '?$query';
    }

    return link;
  }

  /// Generate product deep link
  static String generateProductLink(String productId) {
    return generateDeepLink(type: 'product', id: productId);
  }

  /// Generate order deep link
  static String generateOrderLink(String orderId) {
    return generateDeepLink(type: 'order', id: orderId);
  }

  /// Generate category deep link
  static String generateCategoryLink(String categoryId) {
    return generateDeepLink(type: 'category', id: categoryId);
  }
}

/// Route constants for type safety
class AppRoutes {
  static const String home = '/';
  static const String login = '/login';
  static const String products = '/products';
  static const String cart = '/cart';
  static const String checkout = '/cart/checkout';
  static const String orders = '/orders';
  static const String account = '/account';
  static const String settings = '/settings';
  static const String withdraw = '/withdraw';
  static const String popular = '/popular';
  static const String flashSale = '/flash-sale';
  static const String admin = '/admin';

  /// Get product detail route
  static String productDetail(String productId) => '/products/$productId';

  /// Get order detail route
  static String orderDetail(String orderId) => '/orders/$orderId';

  /// Get deep link route
  static String deepLink(String type, String id) => '/link/$type/$id';
}
